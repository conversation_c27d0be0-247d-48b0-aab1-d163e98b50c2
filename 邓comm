#include <iostream>
#include <fstream>
#include "sqlite3.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <map>
#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/reader.h"
#include "rapidjson/prettywriter.h"
#include "rapidjson/filereadstream.h"
#include "rapidjson/filewritestream.h"
#include "rapidjson/error/en.h"
#include <sys/time.h>
#include <sys/stat.h>
#include "../core/base.h"
#include "unistd.h"
#include "dbi.h"
#include <string>
#include <vector>
#include <sstream>
#include <apistate.h>
#include "SqliteOp.h"
#include <sys/statvfs.h>
#include <iomanip>
#include <chrono>
#include <thread>
#include <cstring> 
#include <vector>
#include <mutex> 
#include <algorithm> // For std::transform
#include <cctype>    // For ::tolower
#include "database_mutex.h"

std::mutex g_database_mutex;
const char* modifytime = "2025-05-19 10:00:00";
const char* opversion = "2.2";
const char* webapiversion = "1.9";
const char* versioninfo =  "1.优化u盘备份内容顺序\
                            2.优化profile数据库表索引\
                            3.优化获取用户权限接口";
                            
#define DATABASE_NAME "/root/press.lx"
#define DATABASE_RESULT "/userdata/resultDb.lx"

#define MEMORY_LIMIT_GB  95 // 占用空间大于95G开始删除
#define MEMORY_REMAUNDER_GB  90 // 占用空间小于90G停止删除
using namespace rapidjson;
bool bserverConnectflag;
int rc;
int rc_result;
int MaxId;
sqlite3* db_result;
sqlite3* db;
sqlite3* db_result_test;
extern bool FileServerControl;
//extern bool Fileconecttest;
                                                                                                                        
//static bool hasVacuumed() {
//    std::ifstream infile("vacuum_done.txt");
//    return infile.good();
//}
//
//static void markVacuumDone() {
//    std::ofstream outfile("vacuum_done.txt");
//    outfile << "done";
//}

// 创建数据表函数
ErrorInfo CreateSdoTable() {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    const char* sql = "CREATE TABLE IF NOT EXISTS SdoData ("
        "id INTEGER PRIMARY KEY AUTOINCREMENT, "
        "skey TEXT UNIQUE, "
        "ukey INTEGER UNIQUE, "
        "name TEXT, "
        "var TEXT"
        ");";
    char* errMsg = nullptr;
    int rc = sqlite3_exec(db, sql, nullptr, nullptr, &errMsg);
    if (rc != SQLITE_OK) {
        sqlite3_free(errMsg);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16206;
    }
    return errorInfo;
}


bool is_interface_up(const std::string& interface) {
    std::string command = "ip link show " + interface;
    FILE* fp = popen(command.c_str(), "r");
    if (fp == nullptr) {
        std::cerr << "Failed to run ip command!" << std::endl;
        return false;
    }

    char buffer[128];
    bool is_up = false;
    while (fgets(buffer, sizeof(buffer), fp) != nullptr) {
        // Check if the interface is up (active)
        if (strstr(buffer, "state UP") != nullptr) {
            is_up = true;
            break;
        }
    }

    fclose(fp);
    return is_up;
}

ErrorInfo renameDatabaseFile(const char* filePath)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    // 生成时间戳字符串，格式：YYYYMMDDHHMMSS
    time_t now = time(nullptr);
    struct tm tm_info;
    localtime_r(&now, &tm_info);  // 使用线程安全版本

    char timestamp[16];
    strftime(timestamp, sizeof(timestamp), "%Y%m%d%H%M%S", &tm_info);

    // 构建新文件名
    char newFileName[256];
    snprintf(newFileName, sizeof(newFileName), "%s_%s.lx", filePath, timestamp);

    std::string original_full_path(filePath);
    std::string originalWalPath = original_full_path + "-wal"; // e.g., /data/db.lx-wal
    std::string originalShmPath = original_full_path + "-shm"; // e.g., /data/db.lx-shm

    // 新的 WAL/SHM 路径基于上面那个可能不符合预期的 newFileName
    std::string newWalPath = std::string(newFileName) + "-wal"; // e.g., /data/db.lx_YYYYMMDDHHMMSS.lx-wal
    std::string newShmPath = std::string(newFileName) + "-shm"; // e.g., /data/db.lx_YYYYMMDDHHMMSS.lx-shm
    std::cout << "[DEBUG] Target WAL archive path: " << newWalPath << std::endl;
    std::cout << "[DEBUG] Target SHM archive path: " << newShmPath << std::endl;
    // --- 结束路径构建 ---


    // --- 检查并重命名 WAL 文件 ---
    std::cout << "[INFO] Checking for WAL file: " << originalWalPath << std::endl;
    if (access(originalWalPath.c_str(), F_OK) == 0) { // F_OK 检查文件是否存在
        std::cout << "[INFO] WAL file exists. Attempting to rename to: " << newWalPath << std::endl;
        errno = 0;
        if (rename(originalWalPath.c_str(), newWalPath.c_str()) != 0) {
            int rename_errno = errno;
            std::cerr << "[WARNING] Failed to rename existing WAL file. Error " << rename_errno << ": " << strerror(rename_errno) << std::endl;
            errorInfoapi.eErrLever = Error;
            errorInfoapi.ErrCode = 16207;
            return errorInfo;
        }
        else {
            std::cout << "[INFO] Successfully renamed WAL file." << std::endl;
        }
    }
    else {
        std::cout << "[INFO] Original WAL file not found, skipping rename." << std::endl;
    }
    // --- 结束 WAL 文件处理 ---


    // --- 检查并重命名 SHM 文件 ---
    std::cout << "[INFO] Checking for SHM file: " << originalShmPath << std::endl;
    if (access(originalShmPath.c_str(), F_OK) == 0) { // F_OK 检查文件是否存在
        std::cout << "[INFO] SHM file exists. Attempting to rename to: " << newShmPath << std::endl;
        errno = 0;
        if (rename(originalShmPath.c_str(), newShmPath.c_str()) != 0) {
            int rename_errno = errno;
            std::cerr << "[WARNING] Failed to rename existing SHM file. Error " << rename_errno << ": " << strerror(rename_errno) << std::endl;
            errorInfoapi.eErrLever = Error;
            errorInfoapi.ErrCode = 16207;
            return errorInfo;
        }
        else {
            std::cout << "[INFO] Successfully renamed SHM file." << std::endl;
        }
    }
    else {
        std::cout << "[INFO] Original SHM file not found, skipping rename." << std::endl;
    }

    // 执行重命名操作
    if (rename(filePath, newFileName) != 0) {
        std::perror("Failed to rename database file");
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 16207;
        return errorInfo;
    }
    else {
        std::cout << "Database file renamed to: " << newFileName << std::endl;
    }
    sleep(2);
    std::string rebootcommand = "reboot";
    int ret = system(rebootcommand.c_str());
    if (ret == 0) {
        std::cout << "重启系统成功" << std::endl;
    }
    else {
        std::cerr << "重启系统失败" << std::endl;
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 16208;
        return errorInfo;
    }
    return errorInfo;

}

ErrorInfo checkAndCheckpointWal(sqlite3* db_handle,
    const std::string& wal_file_path,
    uintmax_t wal_size_threshold_bytes,
    const std::string& db_name)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    // 1. Check if the provided database handle is valid
    if (!db_handle) {
        // Log this issue, as the caller provided an invalid handle for this specific check
        std::cerr << "[WARNING] Cannot check WAL for '" << db_name
            << "' because the provided database handle is null." << std::endl;
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 16209;
        return errorInfo; // Cannot proceed without a valid handle
    }

    // 2. Get WAL file status
    struct stat stat_buf;
    int rc_stat = stat(wal_file_path.c_str(), &stat_buf);

    if (rc_stat != 0) {
        // Handle stat errors
        if (errno == ENOENT) {
            // File not found is expected if checkpointing is working or DB is idle
            std::cerr << "[DEBUG] WAL file '" << db_name << "' ('" << wal_file_path << "') not found." << std::endl;
        }
        else {
            // Log other errors
            std::cerr << "[WARNING] Failed to stat WAL file '" << db_name << "' ('" << wal_file_path
                << "'). Error: " << strerror(errno) << " (errno: " << errno
                << "). Cannot perform checkpoint." << std::endl;
        }
        return errorInfo; // Cannot proceed if stat failed (unless ENOENT)
    }

    // 3. Check file size against threshold
    uintmax_t wal_file_size = static_cast<uintmax_t>(stat_buf.st_size);

    if (wal_file_size > wal_size_threshold_bytes) {
        double size_mb = static_cast<double>(wal_file_size) / (1024.0 * 1024.0);
        double threshold_mb = static_cast<double>(wal_size_threshold_bytes) / (1024.0 * 1024.0);

        std::cout << "[INFO] WAL file '" << db_name << "' (" << size_mb
            << " MB) exceeds threshold (" << threshold_mb
            << " MB). Attempting TRUNCATE checkpoint..." << std::endl;

        // 4. Perform TRUNCATE checkpoint using the provided handle
        int pnLog = 0, pnCkpt = 0;
        int rc_ckpt = sqlite3_wal_checkpoint_v2(db_handle,nullptr,SQLITE_CHECKPOINT_TRUNCATE,&pnLog, &pnCkpt);

        // 5. Log checkpoint results
        if (rc_ckpt == SQLITE_OK) {
            std::cout << "[INFO] Checkpoint TRUNCATE successful '" << db_name << "'. WAL frames: " << pnLog
                << ", Checkpointed: " << pnCkpt << std::endl;
        }
        else if (rc_ckpt == SQLITE_BUSY) {
            std::cerr << "[WARNING] Checkpoint TRUNCATE '" << db_name << "' blocked (SQLITE_BUSY). WAL not fully truncated."
                << " WAL frames: " << pnLog << ", Checkpointed: " << pnCkpt << std::endl;
        }
        else {
            std::cerr << "[ERROR] Checkpoint TRUNCATE '" << db_name << "' failed. Code: " << rc_ckpt
                << ", errmsg: " << sqlite3_errmsg(db_handle) // Use the passed-in handle for error msg
                << std::endl;
            errorInfoapi.eErrLever = Error;
            errorInfoapi.ErrCode = 16210;
            return errorInfo; // Cannot proceed without a valid handle
        }
    }
    return  errorInfo;
}




bool columnExistsViaSchema(sqlite3* db, const std::string& tableName, const std::string& columnName) {
    sqlite3_stmt* stmt = nullptr;
    // 查询 sqlite_schema 获取表的创建语句
    // 注意: sqlite_master 在旧版本中使用，sqlite_schema 是推荐的名称
    std::string sql = "SELECT sql FROM sqlite_schema WHERE type='table' AND lower(name)=lower(?)";
    bool found = false;
    int rc;

    rc = sqlite3_prepare_v2(db, sql.c_str(), -1, &stmt, nullptr);
    if (rc != SQLITE_OK) {
        std::cerr << "[ERROR] Failed to prepare query on sqlite_schema for table '" << tableName
            << "': " << sqlite3_errmsg(db) << std::endl;
        sqlite3_finalize(stmt);
        return false;
    }

    // 绑定表名参数 (大小写不敏感比较已在 SQL 中处理)
    sqlite3_bind_text(stmt, 1, tableName.c_str(), -1, SQLITE_STATIC);

    // 执行查询
    rc = sqlite3_step(stmt);

    if (rc == SQLITE_ROW) {
        // 获取 CREATE TABLE 语句文本
        const unsigned char* createSqlUChar = sqlite3_column_text(stmt, 0);
        if (createSqlUChar) {
            std::string createSqlStr = reinterpret_cast<const char*>(createSqlUChar);
            //std::cout << "[DEBUG] CREATE TABLE statement for '" << tableName << "':\n" << createSqlStr << std::endl; // DEBUG

            // --- 在 CREATE 语句中查找列名 (需要更健壮的查找) ---
            // 简单的查找方法 (大小写不敏感):
            // 使用 strcasestr (POSIX) 或 _stristr (Windows) 或自己实现
            // 需要包含 <cstring>
            // 注意：这种简单查找可能误判 (例如列名是其他列名的一部分)
            // 更可靠的方法是进行更复杂的解析，但通常这个足够

            // 转换为小写进行查找可以避免平台依赖
            std::string lowerCreateSql = createSqlStr;
            std::string lowerColumnName = columnName;
            std::transform(lowerCreateSql.begin(), lowerCreateSql.end(), lowerCreateSql.begin(), ::tolower);
            std::transform(lowerColumnName.begin(), lowerColumnName.end(), lowerColumnName.begin(), ::tolower);

            // 查找 " columnname " 或 "(columnname " 或 ",columnname " 等模式
            // 增加前后空格/逗号/括号来提高准确性，避免子串匹配
            std::string pattern1 = " " + lowerColumnName + " ";
            std::string pattern2 = "(" + lowerColumnName + " ";
            std::string pattern3 = "," + lowerColumnName + " ";
            // 如果是 VIRTUAL 列，可能后面紧跟 GENERATED
            std::string pattern4 = " " + lowerColumnName + "\t"; // 处理可能的制表符
            std::string pattern5 = "," + lowerColumnName + "\t";
            std::string pattern6 = "(" + lowerColumnName + "\t";


            if (lowerCreateSql.find(pattern1) != std::string::npos ||
                lowerCreateSql.find(pattern2) != std::string::npos ||
                lowerCreateSql.find(pattern3) != std::string::npos ||
                lowerCreateSql.find(pattern4) != std::string::npos ||
                lowerCreateSql.find(pattern5) != std::string::npos ||
                lowerCreateSql.find(pattern6) != std::string::npos ||
                // 处理末尾的情况（不太严谨，但可能需要）
                (lowerCreateSql.length() > lowerColumnName.length() &&
                    lowerCreateSql.substr(lowerCreateSql.length() - lowerColumnName.length() - 1) == "," + lowerColumnName)
                )
            {
                //std::cout << "[DEBUG] Found column '" << columnName << "' in CREATE statement." << std::endl; // DEBUG
                found = true;
            }
            else {
                //std::cout << "[DEBUG] Column '" << columnName << "' NOT found in CREATE statement patterns." << std::endl; // DEBUG
            }

        }
        else {
            //std::cerr << "[WARNING] NULL CREATE TABLE statement returned for table '" << tableName << "'." << std::endl;
        }
    }
    else if (rc == SQLITE_DONE) {
        std::cerr << "[WARNING] Table '" << tableName << "' not found in sqlite_schema." << std::endl;
    }
    else {
        // 出错
        std::cerr << "[ERROR] Error stepping through sqlite_schema results for table '" << tableName
            << "': " << sqlite3_errstr(rc) << " (" << rc << ") - " << sqlite3_errmsg(db) << std::endl;
    }

    sqlite3_finalize(stmt);
    return found;
}


// 返回值: true 如果成功或已是最新状态, false 如果发生错误
bool migrateResultTableSchema(const std::string& dbPath) {
    sqlite3* db = nullptr;
    char* zErrMsg = nullptr;
    int rc;
    bool success = true; // 假设成功，除非遇到错误

    const std::string tableName = "result";
    const std::string columnName = "IsCraftOK";

    // 1. 打开数据库 (需要读写权限来修改结构)
    // 注意：确保 SQLite 版本支持生成列 (>= 3.31.0)
    rc = sqlite3_open_v2(dbPath.c_str(), &db, SQLITE_OPEN_READWRITE, nullptr);
    if (rc != SQLITE_OK) {
        std::cerr << "[ERROR] Cannot open database [" << dbPath << "]: " << sqlite3_errmsg(db) << std::endl;
        sqlite3_close(db); // 尝试关闭，即使打开失败也可能分配了句柄
        return false;
    }
    //std::cout << "[INFO] Database opened successfully: " << dbPath << std::endl;

    // 2. 检查 IsCraftOK 列是否存在
    if (!columnExistsViaSchema(db, tableName, columnName)) {
        std::cout << "[INFO] Column '" << columnName << "' not found in table '" << tableName << "'. Attempting to add..." << std::endl;

        // 3. 如果列不存在，执行 ALTER TABLE 添加生成列
        const char* addColumnSql = R"(
            ALTER TABLE result ADD COLUMN IsCraftOK INTEGER GENERATED ALWAYS AS (
                CASE JSON_EXTRACT(EoResult, '$.CraftIsOK')
                    WHEN true THEN 1
                    WHEN false THEN 0
                    ELSE NULL -- 或者根据你的逻辑给一个默认值, 比如 -1
                END
            ) VIRTUAL;
        )";

        rc = sqlite3_exec(db, addColumnSql, nullptr, nullptr, &zErrMsg);
        if (rc != SQLITE_OK) {
            std::cerr << "[ERROR] Failed to add column 'IsCraftOK': " << zErrMsg << std::endl;
            sqlite3_free(zErrMsg); // 释放错误信息内存
            success = false; // 标记失败
        }
        else {
            ;// std::cout << "[INFO] Column 'IsCraftOK' added successfully." << std::endl;
        }
    }
    else {
        ;// std::cout << "[INFO] Column 'IsCraftOK' already exists in table 'result'." << std::endl;
    }

    // 4. 如果上一步成功 (或者列已存在)，尝试创建索引 (使用 IF NOT EXISTS)
    if (success) {
        //std::cout << "[INFO] Ensuring index 'idx_result_iscraftok' exists..." << std::endl;
        const char* createIndexSql = "CREATE INDEX IF NOT EXISTS idx_result_iscraftok ON result(IsCraftOK);";
        rc = sqlite3_exec(db, createIndexSql, nullptr, nullptr, &zErrMsg);
        if (rc != SQLITE_OK) {
            std::cerr << "[ERROR] Failed to create index 'idx_result_iscraftok': " << zErrMsg << std::endl;
            sqlite3_free(zErrMsg);
            success = false; // 标记失败
        }
        else {
            ;// std::cout << "[INFO] Index 'idx_result_iscraftok' ensured (created or already existed)." << std::endl;
        }
    }

    // 5. 关闭数据库连接
    rc = sqlite3_close(db);
    if (rc != SQLITE_OK) {
        // 如果有关闭错误 (例如有未完成的语句)，也应该报告
        std::cerr << "[ERROR] Failed to close database: " << sqlite3_errmsg(db) << std::endl;
        // success = false; // 根据需要决定是否将关闭失败视为整体失败
    }
    else {
        ;// std::cout << "[INFO] Database closed." << std::endl;
    }


    return success;
}


ErrorInfo InitDb(bool* initflag) {
    // 数据库文件名
    *initflag = false;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    bool migrateResult_flag;
    const char* sql2;
    const char* sql1;
    const char* sql_delete;
    std::string  sqlUpgrade{ R"(CREATE TABLE IF NOT EXISTS VersionInfoRecords (
    id                 INTEGER PRIMARY KEY AUTOINCREMENT, 
    webapiversion      VARCHAR(256) NOT NULL,                
    opversion          VARCHAR(256) NOT NULL,                  
    upgrade_time       DATETIME NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')),                      
    info               VARCHAR(512)                          
    );)" };
    int rctimeout;
    char* err_msg = (char*)"";
    const char* sql_commands =
        "CREATE INDEX IF NOT EXISTS idx_finishflag ON Result(Finishflag);"
        "CREATE INDEX IF NOT EXISTS idx_result_fid ON Result(Fid);"
        "CREATE INDEX IF NOT EXISTS idx_result_creationtime ON result(Creationtime);"
        "CREATE INDEX IF NOT EXISTS idx_result_profile_join ON result(ProfileUniquetime, Fid);"
        "CREATE INDEX IF NOT EXISTS idx_result_filters_result ON result(Fid, IsCraftOK);"
        "CREATE INDEX IF NOT EXISTS idx_result_filters_time ON result(Fid, IsCraftOK, Creationtime);";
    if (db_result) {
        sqlite3_close(db_result);
        db_result = nullptr;
    }
    if (db) {
        sqlite3_close(db);
        db = nullptr;
    }
    //sqlite3_shutdown();  // 清理任何未关闭的 SQLite 资源
    //sqlite3_initialize(); 
    
    rc_result = sqlite3_open_v2(DATABASE_RESULT, &db_result, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE , nullptr);
    if (rc_result != SQLITE_OK) {
        printf("初始化数据库失败，无法打开数据库文件: %s\n", sqlite3_errmsg(db_result));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16001;
        goto Errorhandling;
    }
    //sqlite3_exec(db_result, "PRAGMA journal_mode=DELETE;", NULL, NULL, NULL);
    // 3. WAL 模式
    sqlite3_exec(db_result, "PRAGMA journal_mode = WAL;", nullptr, nullptr, &err_msg);
    // 4. 设置繁忙超时（避免锁冲突）
    sqlite3_busy_timeout(db_result, 5000);  // 5秒超时
    // 5. 启用外键约束（数据完整性）
    sqlite3_exec(db_result, "PRAGMA foreign_keys = ON;", 0, 0, 0);
    // 6. 禁用内存映射（防止IO异常导致损坏）
    //sqlite3_exec(db_result, "PRAGMA mmap_size = 0;", 0, 0, 0);
    // 新增：设置 PRAGMA journal_mode = FULL
    // 新增：设置 写回
    sqlite3_exec(db_result, "PRAGMA synchronous = FULL;", 0, 0, 0);
    sqlite3_exec(db_result, "PRAGMA wal_autocheckpoint = 4000;", NULL, NULL, &err_msg);
    //sqlite3_wal_checkpoint_v2(db_result, nullptr, SQLITE_CHECKPOINT_TRUNCATE, nullptr, nullptr);
    //rc_result = sqlite3_exec(db_result, "PRAGMA auto_vacuum = 1;", nullptr, nullptr, NULL);
    //rc_result += sqlite3_exec(db_result, "VACUUM;", nullptr, nullptr, NULL);
    //print_table_indexes(db_result,"result");
    sqlite3_stmt* stmt_result;

    rc_result = sqlite3_prepare_v2(db_result, "SELECT name FROM sqlite_master WHERE type='table' AND name='Result'", -1, &stmt_result, NULL);
    if (rc_result != SQLITE_OK) {
        if (rc_result == 11) {
            const char* errMsg = sqlite3_errmsg(db_result);
            std::cerr << "初始化数据库失败: " << errMsg << std::endl;
            errorInfo = renameDatabaseFile("/userdata/resultDb.lx");
            if (errorInfo.ErrCode != 0) {
                goto Errorhandling;
            }
        }
        std::cerr << "执行插入程序升级记录表失败: " << sqlite3_errmsg(db);
        sqlite3_finalize(stmt_result);
        sqlite3_close_v2(db_result);
        printf("初始化数据库失败，SQL语法错误: %s\n", sqlite3_errmsg(db_result));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16002;
        goto Errorhandling;
    }
    if (sqlite3_step(stmt_result) != SQLITE_ROW) {
        // 表不存在,创建表
        rc_result = sqlite3_exec(db_result, "CREATE TABLE `Result` (`Id` INTEGER PRIMARY KEY AUTOINCREMENT UNIQUE,`Fid` INTEGER  NOT NULL  ,\
            `ProfileUniquetime` DATETIME ,`FidName` VARCHAR(32)  ,`ProfileVer` VARCHAR (32)  ,OverviewInformation JSON,OverviewInformationVer VARCHAR (32),CurveResult JSON,CurveResultVer VARCHAR (32),`EoResult` JSON  , `EoResultVer` VARCHAR (32),\
            `Charts` BLOB ,`ChartsVer` VARCHAR (32)  ,`GlobalVar` VARCHAR(128)   ,`FieldBus` VARCHAR(128) ,\
            `IdGenerator` VARCHAR(128) ,`DataService` VARCHAR(128) ,\
            `ChartInfo` VARCHAR(128) ,`DevBase` VARCHAR(128) ,\
            `BaseInfo` VARCHAR(128) ,`Channel` VARCHAR(128) ,`Calibration` VARCHAR(128) ,\
            `Creationtime` DATETIME NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') ),`Finishflag` INTEGER NOT NULL DEFAULT 0,`Csvflag` INTEGER NOT NULL DEFAULT 0, RefConf JSON,RefResult JSON,Bak1 JSON,Bak2 JSON);", NULL, NULL, NULL);
        if (rc_result != SQLITE_OK) {
            printf( "创建数据库表失败: %s\n",
            sqlite3_errmsg(db_result));
            sqlite3_finalize(stmt_result);
            sqlite3_close_v2(db_result);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16003;
            goto Errorhandling;
        }
    }

    rc_result = sqlite3_exec(db_result, "CREATE INDEX IF NOT EXISTS idx_result_creationtime ON Result(Creationtime);", 0, 0, &err_msg);
    if (rc_result != SQLITE_OK) {
        if (rc_result == 11) {
            const char* errMsg = sqlite3_errmsg(db_result);
            std::cerr << "初始化数据库失败: " << errMsg << std::endl;
            errorInfo = renameDatabaseFile("/userdata/resultDb.lx");
            if (errorInfo.ErrCode != 0) {
                goto Errorhandling;
            }
        }
        sqlite3_finalize(stmt_result);
        sqlite3_close_v2(db_result);
        printf("创建Creationtime索引失败: %s\n", sqlite3_errmsg(db_result));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16130;
        goto Errorhandling;
    }
    sqlite3_finalize(stmt_result);
    //sqlite3_close(db_result);
    //sleep(1);
    migrateResult_flag = migrateResultTableSchema(DATABASE_RESULT);
    if (!migrateResult_flag) {
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16211;
        goto Errorhandling;
    }
    //deleteExistingIndexes(db_result);
    //Wal_Checkpoint();
    rc_result = sqlite3_exec(db_result, sql_commands, 0, 0, &err_msg);
    if (rc_result != SQLITE_OK) {
        printf("详细错误信息: %s\n", err_msg);
        printf("创建Finishflag索引失败: %s\n", sqlite3_errmsg(db_result));
        sqlite3_finalize(stmt_result);
        sqlite3_close_v2(db_result);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16012;
        goto Errorhandling;
    }

    sleep(1);
    sql_delete = "DELETE FROM Result WHERE Finishflag = 0;";

    rc_result = sqlite3_exec(db_result, "BEGIN TRANSACTION;", nullptr, nullptr, &err_msg);  // 开始事务
    if (rc_result != SQLITE_OK) {
        if (rc_result == 11) {
            const char* errMsg = sqlite3_errmsg(db_result);
            std::cerr << "初始化数据库失败: " << errMsg << std::endl;
            errorInfo = renameDatabaseFile("/userdata/resultDb.lx");
            if (errorInfo.ErrCode != 0) {
                goto Errorhandling;
            }
        }
        std::cerr << "SQL error: " << err_msg << std::endl;
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16127;
        goto Errorhandling;
    }

    rc_result = sqlite3_exec(db_result, sql_delete, 0, 0, &err_msg);
    if (rc_result != SQLITE_OK) {
        if (rc_result == 11) {
            const char* errMsg = sqlite3_errmsg(db_result);
            std::cerr << "初始化数据库失败: " << errMsg << std::endl;
            errorInfo = renameDatabaseFile("/userdata/resultDb.lx");
            if (errorInfo.ErrCode != 0) {
                goto Errorhandling;
            }
        }
        std::cerr << "SQL error: " << err_msg << std::endl;
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16127;
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, &err_msg);  // 出错回滚
    }

    rc_result = sqlite3_exec(db_result, "COMMIT;", nullptr, nullptr, &err_msg);  // 提交事务
    if (rc_result != SQLITE_OK) {
        if (rc_result == 11) {
            const char* errMsg = sqlite3_errmsg(db_result);
            std::cerr << "初始化数据库失败: " << errMsg << std::endl;
            errorInfo = renameDatabaseFile("/userdata/resultDb.lx");
            if (errorInfo.ErrCode != 0) {
                goto Errorhandling;
            }
        }
        std::cerr << "SQL error: " << err_msg << std::endl;
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16127;
        goto Errorhandling;
    }

    std::cout << "Record(s) deleted successfully" << std::endl;
    //sqlite3_finalize(stmt_result);
    //sqlite3_close_v2(db_result);
    

    rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE, nullptr);
    if (rc != SQLITE_OK) {
        printf("初始化数据库失败，无法打开数据库文件: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16004;
        goto Errorhandling;
    }
    sqlite3_exec(db, "PRAGMA journal_mode = WAL;", nullptr, nullptr, &err_msg);
    sqlite3_exec(db, "PRAGMA FOREIGN_KEYS=ON;", NULL, NULL, NULL);
    //sqlite3_exec(db, "PRAGMA mmap_size = 0;", 0, 0, 0);
    sqlite3_exec(db, "PRAGMA SYNCHRONOUS=FULL", NULL, NULL, NULL);
    sqlite3_exec(db, "PRAGMA wal_autocheckpoint=4000;", NULL, NULL, NULL);
    rctimeout = sqlite3_busy_timeout(db, 2000); 
    if (rc != SQLITE_OK) {
        std::cerr << "Failed to set busy timeout: " << sqlite3_errmsg(db) << std::endl;
    }
    sqlite3_stmt* stmt;
    rc = sqlite3_prepare_v2(db, "SELECT name FROM sqlite_master WHERE type='table' AND name='Sdo'", -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        std::cerr << "执行插入程序升级记录表失败: " << sqlite3_errmsg(db);
        sqlite3_finalize(stmt);
        sqlite3_close_v2(db);
        //printf("初始化数据库失败，SQL语法错误: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16005;
        goto Errorhandling;
    }
    if (sqlite3_step(stmt) != SQLITE_ROW) {
        // 表不存在,创建表
        rc = sqlite3_exec(db, "CREATE TABLE `Sdo` (`Id` INTEGER NOT NULL,`Name` VARCHAR(32) NOT NULL,`Version` VARCHAR(32) NOT NULL,`Content` JSON NOT NULL,`Updatetime` DATETIME NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') ) ,PRIMARY KEY(`Id`),UNIQUE(Name, Version) ON CONFLICT REPLACE); ", NULL, NULL, NULL);
        if (rc != SQLITE_OK) {
            printf("创建数据库表失败: %s\n", sqlite3_errmsg(db));
            sqlite3_finalize(stmt);
            sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16006;
            goto Errorhandling;
        }
        //const char* sql1 = "CREATE TRIGGER IF NOT EXISTS update_create_time_insert AFTER INSERT ON Profile BEGIN UPDATE Profile SET Updatetime = CURRENT_TIMESTAMP WHERE rowid = new.id; END;";
        sql1 = "CREATE TRIGGER IF NOT EXISTS UpdatetimeSdo AFTER UPDATE ON Sdo BEGIN UPDATE Sdo SET Updatetime = CURRENT_TIMESTAMP WHERE rowid = new.id; END;";
        rc = sqlite3_exec(db, sql1, 0, 0, &err_msg);
        if (rc != SQLITE_OK) {
            printf("创建触发器失败: %s\n", err_msg);
            sqlite3_free(err_msg);
            sqlite3_finalize(stmt);
            sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16007;
            goto Errorhandling;
        }
    }
    sqlite3_reset(stmt);
    if (rc != SQLITE_OK) {
        printf("重置准备语句对象失败: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt);
        sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16008;
        goto Errorhandling;
    }
    rc = sqlite3_prepare_v2(db, "SELECT name FROM sqlite_master WHERE type='table' AND name='Blob'", -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        printf("初始化数据库失败，SQL语法错误: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt);
        sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16009;
        goto Errorhandling;
    }
    if (sqlite3_step(stmt) != SQLITE_ROW) {
        // 表不存在,创建表
        rc = sqlite3_exec(db, "CREATE TABLE `Blob` (`Id` INTEGER NOT NULL,`Name` VARCHAR(32) NOT NULL ,`Version` VARCHAR(32) NOT NULL ,`Content` BLOB NOT NULL,`Updatetime` DATETIME NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') ) ,PRIMARY KEY(`Id`),UNIQUE(Name, Version) ON CONFLICT REPLACE); ", NULL, NULL, NULL);
        if (rc != SQLITE_OK) {
            printf("创建数据库表失败: %s\n", sqlite3_errmsg(db));
            sqlite3_finalize(stmt);
            sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16010;
            goto Errorhandling;
        }
        //const char* sql1 = "CREATE TRIGGER IF NOT EXISTS update_create_time_insert AFTER INSERT ON Profile BEGIN UPDATE Profile SET Updatetime = CURRENT_TIMESTAMP WHERE rowid = new.id; END;";
        sql2 = "CREATE TRIGGER IF NOT EXISTS UpdatetimeBlob AFTER UPDATE ON Blob BEGIN UPDATE Blob SET Updatetime = CURRENT_TIMESTAMP WHERE rowid = new.id; END;";
        rc = sqlite3_exec(db, sql2, 0, 0, &err_msg);
        if (rc!= SQLITE_OK) {
            printf("创建触发器失败: %s\n", sqlite3_errmsg(db));
            sqlite3_finalize(stmt);
            sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16011;
            goto Errorhandling;
        }
    }
    sqlite3_finalize(stmt);

    sqlite3_stmt* stmt_Statistics;
    rc = sqlite3_prepare_v2(db, "SELECT name FROM sqlite_master WHERE type='table' AND name='StatisticsData'", -1, &stmt_Statistics, NULL);
    if (rc != SQLITE_OK) {
        printf("初始化数据库失败，sql语句语法错误: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt_Statistics);
        sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16014;
        goto Errorhandling;
    }
    if (sqlite3_step(stmt_Statistics) != SQLITE_ROW) {
        rc = sqlite3_exec(db, "CREATE TABLE `StatisticsData` (`Id` INTEGER ,`Fid` INTEGER  UNIQUE,`Ver` VARCHAR(32) ,`Content` JSON ,`Creationtime` DATETIME  DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') ) ,PRIMARY KEY(`Id`)); ", NULL, NULL, NULL);
        if (rc != SQLITE_OK) {
            printf("创建数据库表失败: %s\n", sqlite3_errmsg(db));
            sqlite3_finalize(stmt_Statistics);
            sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16015;
            goto Errorhandling;
        }
    }
    sqlite3_finalize(stmt_Statistics);

    sqlite3_stmt* stmt_version;
    rc = sqlite3_prepare_v2(db, "SELECT name FROM sqlite_master WHERE type='table' AND name='DBversion'", -1, &stmt_version, NULL);
    if (rc != SQLITE_OK) {
        printf("初始化数据库失败，sql语句语法错误: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt_version);
        sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16102;
        goto Errorhandling;
    }
    if (sqlite3_step(stmt_version) != SQLITE_ROW) {
        rc = sqlite3_exec(db, "CREATE TABLE IF NOT EXISTS DBversion (`version` REAL PRIMARY KEY, `Creationtime` DATETIME  DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') )); ", NULL, NULL, NULL);
        if (rc != SQLITE_OK) {
            printf("创建数据库表失败: %s\n", sqlite3_errmsg(db));
            sqlite3_finalize(stmt_version);
            sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16103;
            goto Errorhandling;
        }
    }
    sqlite3_finalize(stmt_version);
  //  sqlite3_close_v2(db);
    sqlite3_stmt* stmt_HardwareConfiguration;
    rc = sqlite3_prepare_v2(db, "SELECT name FROM sqlite_master WHERE type='table' AND name='HardwareConfiguration'", -1, &stmt_HardwareConfiguration, NULL);
    if (rc != SQLITE_OK) {
        printf("初始化数据库失败，sql语句语法错误: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt_HardwareConfiguration);
        sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16104;
        goto Errorhandling;      
    }
    if (sqlite3_step(stmt_HardwareConfiguration) != SQLITE_ROW) {
        rc = sqlite3_exec(db, "CREATE TABLE IF NOT EXISTS HardwareConfiguration ( `Model` VARCHAR(32) NOT NULL, `ControlMode` VARCHAR(32) NOT NULL,`Drive` JSON NOT NULL,`Sensor` JSON ); ", NULL, NULL, NULL);
        if (rc != SQLITE_OK) {
            printf("创建数据库表失败: %s\n", sqlite3_errmsg(db));
            sqlite3_finalize(stmt_HardwareConfiguration);
            sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16105;
            goto Errorhandling;
        }
    }
    sqlite3_finalize(stmt_HardwareConfiguration);
    rc = sqlite3_exec(db, sqlUpgrade.c_str(),NULL, NULL, NULL);
    if (rc != SQLITE_OK)
    {
        printf("创建程序升级数据库表失败: %s\n", sqlite3_errmsg(db));
        std::cerr << "创建程序升级记录表失败,错误码 " << rc << std::endl;
        sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16131;
        goto Errorhandling;
    }
    sleep(1);
    sqlite3_stmt* versionstmt ;
    rc = sqlite3_prepare_v2(db,"INSERT INTO VersionInfoRecords (webapiversion, opversion, info) SELECT ?, ?, ? WHERE NOT EXISTS (SELECT 1 FROM VersionInfoRecords WHERE webapiversion = ? AND opversion = ?);" ,-1,&versionstmt, NULL);
    rc += sqlite3_bind_text(versionstmt, 1, webapiversion, strlen(webapiversion), SQLITE_TRANSIENT);
    rc += sqlite3_bind_text(versionstmt, 2, opversion, strlen(opversion), SQLITE_TRANSIENT);
    rc += sqlite3_bind_text(versionstmt, 3, versioninfo, strlen(versioninfo), SQLITE_TRANSIENT);
    rc += sqlite3_bind_text(versionstmt, 4, webapiversion, strlen(webapiversion), SQLITE_TRANSIENT);
    rc += sqlite3_bind_text(versionstmt, 5, opversion, strlen(opversion), SQLITE_TRANSIENT);
    if (rc == SQLITE_OK)
    {
        if (sqlite3_step(versionstmt) != SQLITE_DONE)
        {
            printf("插入程序升级记录表失败: %s\n", sqlite3_errmsg(db));
            std::cerr << "插入程序升级记录表失败"<< std::endl;
            sqlite3_finalize(versionstmt);
            sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16132;
            goto Errorhandling;
        }
    }
    else {
        std::cerr << "预编译插入程序升级记录表sql失败" << std::endl;
        printf("预编译插入程序升级记录表sql失败: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(versionstmt);
        sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16133;
        goto Errorhandling;
    }
    sqlite3_finalize(versionstmt);

    sql2 = "CREATE TABLE IF NOT EXISTS AlarmFatalRecord(Id INTEGER UNIQUE PRIMARY KEY AUTOINCREMENT, AlarmCode VARCHAR(16) NOT NULL, UpdateTime DATETIME DEFAULT(strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime'))NOT NULL, Brief VARCHAR(256), Fatalerrorinformation JSON);";
    rc = sqlite3_exec(db, sql2, 0, 0, &err_msg);
    if (rc != SQLITE_OK) {
        printf("创建严重错误码表失败: %s\n", err_msg);
        sqlite3_free(err_msg);
        //sqlite3_finalize(stmt);
        sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16146;
        goto Errorhandling;
    }
    else{
        sleep(1);
        sql1 =
            "CREATE TRIGGER IF NOT EXISTS insert_into_AlarmRecord "
            "AFTER INSERT ON AlarmFatalRecord "
            "FOR EACH ROW "
            "BEGIN "
            "INSERT INTO AlarmRecord(AlarmCode, UpdateTime, Brief) "
            "VALUES( "
            "NEW.AlarmCode, "
            "COALESCE(NEW.UpdateTime, CURRENT_TIMESTAMP), "
            "NEW.Brief "
            "); "
            "END;";
        rc = sqlite3_exec(db, sql1, 0, 0, &err_msg);
        if (rc != SQLITE_OK) {
            printf("创建错误码表触发器失败: %s\n", err_msg);
            sqlite3_free(err_msg);
            //sqlite3_finalize(stmt);
            sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16145;
            goto Errorhandling;
        }
    }
    errorInfo = CreateSdoTable();
    if (errorInfo.ErrCode != 0) {
        goto Errorhandling;
    }
    *initflag = true;
    //Wal_Checkpoint();
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"InitDb", "");
    return  errorInfo;
}

/*SelectHardwareConfiguration		          查找sqlite数据库工艺内容的函数
* @param[in]     Model                  = 需要查找的Model
* @param[in]     ControlMode            = 需要查找的ControlMode
* @param[in]     spItem                 = 需要查找到的字段
* @return        errorInfo.ErrCode非0   = 查找失败
* @return        str                    = 查找到的设置内容
* @return        str = " "              = 查找到的内容为空
*/
ErrorInfo SelectHardwareConfiguration(char* Model, char* ControlMode, char* spItem, std::string* str) {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    char sql[1000];

    sprintf(sql, "SELECT %s FROM HardwareConfiguration WHERE Model =? AND ControlMode =?", spItem);

    // 准备查询 
    sqlite3_stmt* res;
    rc = sqlite3_prepare_v2(db, sql, -1, &res, 0);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(res);
        //sqlite3_close_v2(db);
        printf("选择设备项失败，sql语句语法错误: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16106;
        goto Errorhandling;
    }

    rc += sqlite3_bind_text(res, 1, Model, -1, SQLITE_STATIC);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(res);
        //sqlite3_close_v2(db);
        printf("绑定 Model 失败: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16107;
        goto Errorhandling;
    }

    rc += sqlite3_bind_text(res, 2, ControlMode, -1, SQLITE_STATIC);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(res);
        //sqlite3_close_v2(db);
        printf("绑定 ControlMode 失败: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16108;
        goto Errorhandling;
    }
    rc = sqlite3_step(res);
    if (rc != SQLITE_ROW) {
        sqlite3_finalize(res);
        //sqlite3_close_v2(db);
        printf("执行选择设备项失败，没有匹配的记录: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16109;
        goto Errorhandling;
    }
    else {
        if (sqlite3_column_blob(res, 0) == nullptr) {
            sqlite3_finalize(res);
            //sqlite3_close_v2(db);
            *str = " ";
        }
        else {
            *str = reinterpret_cast<const char*>(sqlite3_column_blob(res, 0));
            sqlite3_finalize(res);
            //sqlite3_close_v2(db);
        }
    }
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"SelectProfileItem", "");
    return  errorInfo;
}

/*sqlitemigrations		          查找sqlite数据库工艺内容的函数
* @param[in]     current_version        = 当前数据库版本号
* @return        errorInfo.ErrCode非0   = 查找失败
*/
ErrorInfo sqlitemigrations(double current_version) {
    struct Migration {
        double version;
        const char* script;
    };
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    std::vector<Migration> migrations = {
        {1.1, "ALTER TABLE your_table ADD COLUMN new_column TEXT;"},
        {2.0, R"(
            CREATE TABLE new_table (
                id INTEGER PRIMARY KEY,
                new_column_name TEXT
                -- 其他字段
            );
            INSERT INTO new_table (id, new_column_name)
            SELECT id, old_column_name
            FROM your_table;
            DROP TABLE your_table;
            ALTER TABLE new_table RENAME TO your_table;
        )"}
    };
    double version = 0.0;
    const char* query = "SELECT MAX(version) FROM dbversion;";
    const char* insert_query = "INSERT INTO dbversion (version) VALUES (?); ";
    sqlite3_stmt* stmt;
    sqlite3_stmt* script_stmt;
    sqlite3_stmt* insert_stmt;
    if (sqlite3_prepare_v2(db, query, -1, &stmt, 0) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            version = sqlite3_column_double(stmt, 0);
            for (const auto& migration : migrations) {
                if (migration.version > current_version) {
                    if (sqlite3_prepare_v2(db, migration.script, -1, &script_stmt, 0) == SQLITE_OK) {
                        if (sqlite3_step(script_stmt) == SQLITE_ROW) {
                            if(migration.version> version){
                                version = migration.version;
                            }
                            printf("版本更新成功");
                        }
                        else {
                            printf("执行数据库升级失败: %s\n", sqlite3_errmsg(db));
                            errorInfo.eErrLever = Error;
                            errorInfo.ErrCode = 16113;
                        }
                    }
                    else {
                        printf("数据库升级失败，sql语句语法错误: %s\n", sqlite3_errmsg(db));
                        errorInfo.eErrLever = Error;
                        errorInfo.ErrCode = 16112;
                    }
                }
            }
        }
        else {
            //sqlite3_close_v2(db);
            printf("执行查找最大版号失败: %s\n", sqlite3_errmsg(db));
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16111;
        }
    }
    else {
        //sqlite3_close_v2(db);
        printf("查找最大版号失败，sql语句语法错误: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16110;
    }
    if (sqlite3_prepare_v2(db, insert_query, -1, &insert_stmt, 0) == SQLITE_OK) {
        int rc = sqlite3_bind_int(insert_stmt, 1, version);
        if (rc == SQLITE_OK) {
            rc = sqlite3_step(insert_stmt);
            if (rc == SQLITE_DONE) {
                printf("版本号更新成功");
            }
            else {
                errorInfo.eErrLever = Error;
                errorInfo.ErrCode = 16116;
            }
        }
        else {
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16115;
        }
    }
    else {
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16114;
    }

    sqlite3_finalize(stmt);
    sqlite3_finalize(script_stmt);
    sqlite3_finalize(insert_stmt);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"sqlitemigrations", "");
    return  errorInfo;
}

char* ReadFile(char* filename) {

    FILE* file = fopen(filename, "r");
    if (!file) {
        return NULL;
    }

    fseek(file, 0, SEEK_END);
    long size = ftell(file);
    rewind(file);

    void* temp = malloc(size + 1);
    char* json = (char*)temp;
    fread(json, 1, size, file);
    json[size] = '\0';

    fclose(file);

    return json;
}

/*SelectProfileItem		          查找sqlite数据库工艺内容的函数    
* @param[in]     Fid                    = 需要查找的工艺的fileid
* @param[in]     Ver                    = 需要查找的工艺的版本号
* @param[in]     spItem                 = 需要查找到的工艺中的字段
* @return        errorInfo.ErrCode非0   = 查找失败
* @return        str                    = 查找到的工艺内容
* @return        str = " "              = 查找到的内容为空
*/
ErrorInfo SelectProfileItem(int FID, char* Ver, char* spItem, std::string* str) {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    /*sqlite3* db;
    int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("选择工艺项失败，无法打开数据库文件: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16016;
        goto Errorhandling;
    }*/
    char sql[1000];

    sprintf(sql, "SELECT %s FROM profile WHERE Fid =? AND Ver =?", spItem);

    // 准备查询 
    sqlite3_stmt* res;
    rc = sqlite3_prepare_v2(db, sql, -1, &res, 0);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(res);
        //sqlite3_close_v2(db);
        printf("选择工艺项失败，sql语句语法错误: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16017;
        goto Errorhandling;
    }

    rc += sqlite3_bind_int(res, 1, FID);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(res);
        //sqlite3_close_v2(db);
        printf("绑定 FID 失败: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16018;
        goto Errorhandling;
    }

    rc += sqlite3_bind_text(res, 2, Ver, -1, SQLITE_STATIC);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(res);
        //sqlite3_close_v2(db);
        printf("绑定 Ver 失败: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16019;
        goto Errorhandling;
    }
    rc = sqlite3_step(res);
    if (rc != SQLITE_ROW) {
        sqlite3_finalize(res);
        //sqlite3_close_v2(db);
        printf("选择工艺项失败，没有匹配的工艺: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16020;
        goto Errorhandling;
    }
    else {
        if (sqlite3_column_blob(res, 0) == nullptr) {
            sqlite3_finalize(res);
            //sqlite3_close_v2(db);
            *str = " ";
        }
        else {
            *str = reinterpret_cast<const char*>(sqlite3_column_blob(res, 0));
            sqlite3_finalize(res);
            //sqlite3_close_v2(db);
        }
    }
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"SelectProfileItem", "");
    return  errorInfo;
}

/*InsertProfile		               插入sqlite数据库工艺内容的函数
* @param[in]     pfcontent              = 需要插入的工艺内容
* @return        errorInfo.ErrCode非0   = 插入工艺失败
* @return        errorInfo.ErrCode = 0  = 插入工艺成功
*/
ErrorInfo InsertProfile(char* ProContent) {
    std::string eo_str;
    std::string seqs;
    std::string advancedSet;
    std::string IdGenerator;
    std::string channel;
    std::string local_vars;
    std::string ver_no;
    std::string name;
    std::string operators;
    std::string properties;
    std::string created_time;
    rapidjson::Document json_req;
    std::string update_sql;
    int fid;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    //sqlite3* db;
    //int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    sqlite3_create_function(db, "md5", 1, SQLITE_UTF8, NULL, md5, NULL, NULL);
    // 解析请求JSON
   /* if (rc != SQLITE_OK) {
        printf("新增或修改工艺项失败，无法打开数据库文件: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16021;
        goto Errorhandling;
    }*/
    json_req.Parse(ProContent);
    fid = json_req["Fid"].GetInt();
    if (json_req["EO"].IsString()) {
        eo_str = json_req["EO"].GetString();
    }
    else if (json_req["EO"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["EO"].Accept(writer);
        eo_str = buffer.GetString();
    }
    if (json_req["Sequence"].IsString()) {
        seqs = json_req["Sequence"].GetString();
    }
    else if (json_req["Sequence"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Sequence"].Accept(writer);
        seqs = buffer.GetString();
    }
    if (json_req["AdvancedSet"].IsString()) {
        advancedSet = json_req["AdvancedSet"].GetString();
    }
    else if (json_req["AdvancedSet"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["AdvancedSet"].Accept(writer);
        advancedSet = buffer.GetString();
    }
    if (json_req["IdGenerator"].IsString()) {
        IdGenerator = json_req["IdGenerator"].GetString();
    }
    else if (json_req["IdGenerator"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["IdGenerator"].Accept(writer);
        IdGenerator = buffer.GetString();
    }
    if (json_req["Channel"].IsString()) {
        channel = json_req["Channel"].GetString();
    }
    else if (json_req["Channel"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Channel"].Accept(writer);
        channel = buffer.GetString();
    }
    if (json_req["LocalVar"].IsArray()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["LocalVar"].Accept(writer);
        local_vars = buffer.GetString();
    }
    else if (json_req["LocalVar"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["LocalVar"].Accept(writer);
        local_vars = buffer.GetString();
    }
    if (json_req["Ver"].IsString()) {
        ver_no = json_req["Ver"].GetString();
    }
    else if (json_req["Ver"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Ver"].Accept(writer);
        ver_no = buffer.GetString();
    }
    if (json_req["Name"].IsString()) {
        name = json_req["Name"].GetString();
    }
    else if (json_req["Name"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Name"].Accept(writer);
        name = buffer.GetString();
    }
    if (json_req["Operator"].IsString()) {
        operators = json_req["Operator"].GetString();
    }
    else if (json_req["Operator"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Operator"].Accept(writer);
        operators = buffer.GetString();
    }
    if (json_req["Properties"].IsString()) {
        properties = json_req["Properties"].GetString();
    }
    else if (json_req["Properties"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Properties"].Accept(writer);
        properties = buffer.GetString();
    }
    update_sql = "INSERT OR REPLACE INTO profile (Fid, EO, Sequence, AdvancedSet, IdGenerator, Channel, LocalVar, Ver, Name, Operator, Properties ,UpdateTime)VALUES (?,?,?,?,?,?,?,?,?,?,?, strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime'));";
    // 执行更新语句
    sqlite3_stmt* update_stmt;
    rc = sqlite3_prepare_v3(db, update_sql.c_str(), update_sql.length(), 0, &update_stmt, NULL);
    //rc = sqlite3_prepare_v2(db, update_sql.c_str(), -1, &update_stmt, NULL);
    rc += sqlite3_bind_int(update_stmt, 1, fid);
    rc += sqlite3_bind_blob(update_stmt, 2, eo_str.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 3, seqs.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 4, advancedSet.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 5, IdGenerator.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 6, channel.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 7, local_vars.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 8, ver_no.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 9, name.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 10, operators.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 11, properties.c_str(), -1, SQLITE_STATIC);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(update_stmt);
        //sqlite3_close_v2(db);
        printf("新增或修改工艺项失败，SQL语法错误或绑定参数失败: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16022;
        goto Errorhandling;
    }
    else{
        rc = sqlite3_step(update_stmt);
    }
    if (rc == SQLITE_DONE) {
        //rc = sqlite3_exec(db, "PRAGMA wal_checkpoint(TRUNCATE)", nullptr, nullptr, 0);
        sqlite3_finalize(update_stmt);
        //sqlite3_close_v2(db);
        goto Errorhandling;
    }
    else {
        printf("执行新增或修改工艺项失败: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16023;
    }
    sqlite3_finalize(update_stmt);
    //sqlite3_close_v2(db);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"InsertProfile", "");
    return  errorInfo;
}

/*UpdateProfile		               更新sqlite数据库工艺内容的函数
* @param[in]     Fid                    = 需要更新的工艺的fileid
* @param[in]     Ver                    = 需要更新的工艺的版本号
* @return        errorInfo.ErrCode非0   = 更新工艺失败
* @return        errorInfo.ErrCode = 0  = 更新工艺成功
*/
ErrorInfo UpdateProfile(char* ProContent) {
    // 解析请求JSON
    char* update_sql;
    int fid;
    rapidjson::Document json_req;
    json_req.Parse(ProContent);
    std::string eo_str;
    std::string seqs;
    std::string advancedSet;
    std::string IdGenerator;
    std::string channel;
    std::string local_vars;
    std::string ver_no;
    std::string name;
    std::string operators;
    std::string properties;
    std::string created_time;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    //sqlite3* db;
    //int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    sqlite3_create_function(db, "md5", 1, SQLITE_UTF8, NULL, md5, NULL, NULL);
   /* if (rc != SQLITE_OK) {
        printf("新增或修改工艺项失败，无法打开数据库文件: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16024;
        goto Errorhandling;
    }*/
    // 解析请求JSON
    fid = json_req["Fid"].GetInt();
    if (json_req["EO"].IsString()) {
        eo_str = json_req["EO"].GetString();
    }
    else if (json_req["EO"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["EO"].Accept(writer);
        eo_str = buffer.GetString();
    }
    if (json_req["Sequence"].IsString()) {
        seqs = json_req["Sequence"].GetString();
    }
    else if (json_req["Sequence"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Sequence"].Accept(writer);
        seqs = buffer.GetString();
    }
    if (json_req["AdvancedSet"].IsString()) {
        advancedSet = json_req["AdvancedSet"].GetString();
    }
    else if (json_req["AdvancedSet"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["AdvancedSet"].Accept(writer);
        advancedSet = buffer.GetString();
    }
    if (json_req["IdGenerator"].IsString()) {
        IdGenerator = json_req["IdGenerator"].GetString();
    }
    else if (json_req["IdGenerator"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["IdGenerator"].Accept(writer);
        IdGenerator = buffer.GetString();
    }
    if (json_req["Channel"].IsString()) {
        channel = json_req["Channel"].GetString();
    }
    else if (json_req["Channel"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Channel"].Accept(writer);
        channel = buffer.GetString();
    }
    if (json_req["LocalVar"].IsArray()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["LocalVar"].Accept(writer);
        local_vars = buffer.GetString();
    }
    else if (json_req["LocalVar"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["LocalVar"].Accept(writer);
        local_vars = buffer.GetString();
    }
    if (json_req["Ver"].IsString()) {
        ver_no = json_req["Ver"].GetString();
    }
    else if (json_req["Ver"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Ver"].Accept(writer);
        ver_no = buffer.GetString();
    }
    if (json_req["Name"].IsString()) {
        name = json_req["Name"].GetString();
    }
    else if (json_req["Name"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Name"].Accept(writer);
        name = buffer.GetString();
    }
    if (json_req["Operator"].IsString()) {
        operators = json_req["Operator"].GetString();
    }
    else if (json_req["Operator"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Operator"].Accept(writer);
        operators = buffer.GetString();
    }
    if (json_req["Properties"].IsString()) {
        properties = json_req["Properties"].GetString();
    }
    else if (json_req["Properties"].IsObject()) {
        rapidjson::StringBuffer buffer;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
        json_req["Properties"].Accept(writer);
        properties = buffer.GetString();
    }
    update_sql = (char*)"INSERT OR REPLACE INTO Profile (Fid, EO, Sequence, AdvancedSet, IdGenerator, Channel, LocalVar, Ver, Name, Operator, Properties )VALUES (?,?,?,?,?,?,?,?,?,?,?);";
    // 执行更新语句
    sqlite3_stmt* update_stmt;
    rc = sqlite3_prepare_v2(db, update_sql, -1, &update_stmt, NULL);
    rc += sqlite3_bind_int(update_stmt, 1, fid);
    rc += sqlite3_bind_blob(update_stmt, 2, eo_str.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 3, seqs.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 4, advancedSet.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 5, IdGenerator.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 6, channel.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 7, local_vars.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 8, ver_no.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 9, name.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 10, operators.c_str(), -1, SQLITE_STATIC);
    rc += sqlite3_bind_blob(update_stmt, 11, properties.c_str(), -1, SQLITE_STATIC);
    if (rc != SQLITE_OK) {
        printf("新增或修改工艺项失败，SQL语法错误或绑定参数失败: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(update_stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16025;
        goto Errorhandling;
    }
    else if (sqlite3_step(update_stmt) == SQLITE_DONE) {
        sqlite3_finalize(update_stmt);
        //sqlite3_close_v2(db);
        goto Errorhandling;
    }
    printf("执行新增或修改工艺项失败: %s\n", sqlite3_errmsg(db));
    sqlite3_finalize(update_stmt);
    //sqlite3_close_v2(db);
    errorInfo.eErrLever = Error;
    errorInfo.ErrCode = 16026;
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"UpdateProfile", "");
    return  errorInfo;
}

/*DeleteProfile		               删除sqlite数据库工艺记录的函数
* @param[in]     Fid                    = 需要删除的工艺的fileid
* @return        errorInfo.ErrCode非0   = 删除工艺失败
* @return        errorInfo.ErrCode = 0  = 删除工艺成功
*/
ErrorInfo DeleteProfile(int Fid) {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    char delete_sql[200];
    //sqlite3* db;
    char select_sql[200];
    /*int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("删除工艺项失败，无法打开数据库文件: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16027;
        goto Errorhandling;
    }*/
    sprintf(select_sql, "SELECT * FROM Profile WHERE Fid=%d", Fid);

    // 检查记录是否存在
    sqlite3_stmt* select_stmt;
    rc = sqlite3_prepare_v2(db, select_sql, -1, &select_stmt, NULL);
    if (rc != SQLITE_OK || sqlite3_step(select_stmt) != SQLITE_ROW) {
        printf("删除工艺项失败，SQL语法错误或不存在删除项: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(select_stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16028;
        goto Errorhandling;
    }
    else{
        sprintf(delete_sql, "DELETE FROM Profile WHERE Fid=%d", Fid);
        sqlite3_stmt* delete_stmt;
        rc = sqlite3_prepare_v2(db, delete_sql, -1, &delete_stmt, NULL);
        if (rc == SQLITE_OK && sqlite3_step(delete_stmt) == SQLITE_DONE) {
            //rc = sqlite3_exec(db, "PRAGMA wal_checkpoint(TRUNCATE)", nullptr, nullptr, 0);
            sqlite3_finalize(select_stmt);
            sqlite3_finalize(delete_stmt);
            //sqlite3_close_v2(db);
            goto Errorhandling;
        }
        else {
            printf("删除工艺项失败，SQL语法错误或执行删除失败: %s\n", sqlite3_errmsg(db));
            sqlite3_finalize(select_stmt);
            sqlite3_finalize(delete_stmt);
            //sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16029;
            goto Errorhandling;
        }
    }
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"DeleteProfile", "");
    return  errorInfo;
}

/*WriteJsonOp	                       插入sqlite数据库text类型数据的函数
* @param[in]     name                   = 需要新增或更新的记录名称
* @param[in]     content                = 新增或更新的文件内容
* @param[in]     version                = 新增或更新的记录的版本号
* @return        errorInfo.ErrCode非0   = 新增或更新记录失败
* @return        errorInfo.ErrCode = 0  = 新增或更新记录成功
*/
ErrorInfo WriteJsonOp(char* name, char* content, char* version) {
    char* query_sql;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    int count = 0;
    /* sqlite3* db;
    int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16030;
        goto Errorhandling;
    }*/
    sqlite3_stmt* stmt;
    query_sql = (char*)"INSERT OR REPLACE INTO Sdo (Name,Content,Version) VALUES (?, ?, ?);";
    int rc = sqlite3_prepare_v2(db, query_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        printf("sql语句语法错误: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16031;
        goto Errorhandling;
    }
    rc = sqlite3_bind_text(stmt, 2, content, -1, SQLITE_STATIC);
    rc +=sqlite3_bind_text(stmt, 1, name, -1, SQLITE_STATIC);
    rc +=sqlite3_bind_text(stmt, 3, version, -1, SQLITE_STATIC);
    if (rc == SQLITE_OK) {
        rc = sqlite3_step(stmt);
        if (rc != SQLITE_DONE) {
            printf("插入记录失败: %s\n", sqlite3_errmsg(db));
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16032;
            goto Errorhandling;
        }
    }
    //rc = sqlite3_exec(db, "PRAGMA wal_checkpoint(TRUNCATE)", nullptr, nullptr, 0);
    sqlite3_finalize(stmt);
    // 关闭数据库连接
    //sqlite3_close_v2(db);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"WriteJsonOp", "");
    return  errorInfo;
}

bool readLastIP(const std::string& filePath) {
    std::ifstream ipconfigFile(filePath);
    std::string line, lastIP;

    if (!ipconfigFile.is_open()) {
        std::cerr << "无法打开文件：" << filePath << std::endl;
        return false;
    }

    while (std::getline(ipconfigFile, line)) {
        lastIP = line;  // 记录最后一行
    }

    ipconfigFile.close();

    if (!lastIP.empty()) {
        opipeth3 = lastIP; // 赋值给全局变量
        return true;
    }
    else {
        return false;
    }
}


/*ReadJsonOp		                查找sqlite数据库text类型数据的函数
* @param[in]     name                 = 需要查找的记录名称
* @param[in]     version              = 需要查找的记录的版本号
* @return        errorInfo.ErrCode非0 = 查找数据失败
* @return        content              = 查找到的内容数据
* @return        " "                  = 查找到的内容为空
*/
ErrorInfo ReadJsonOp(char* name, char* version, std::string* content) {
    const unsigned char* result;
    std::string select_sql;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    /* sqlite3* db;
    int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16033;
        goto Errorhandling;
    }*/
    sqlite3_stmt* stmt;
    select_sql = "SELECT Content FROM Sdo WHERE Name=? AND Version=?";
    rc = sqlite3_prepare_v3(db, select_sql.c_str(), select_sql.length(), 0, &stmt, NULL);
    rc += sqlite3_bind_text(stmt, 1, name, -1, SQLITE_STATIC);
    rc += sqlite3_bind_text(stmt, 2, version, -1, SQLITE_STATIC);
    //printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16034;
        goto Errorhandling;
    }
    
    rc = sqlite3_step(stmt);
    if (rc == SQLITE_ROW) {
        result = sqlite3_column_text(stmt, 0);
        if (result != NULL) {
            *content = reinterpret_cast<const char*>(result);
        }
        else {
            *content = " ";
        }
        bool read_operate = readLastIP("/root/ipconfig.txt");
        if (read_operate != true) {
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16033;
            goto Errorhandling;
        }
        else {
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            goto Errorhandling;
        }
    }
    else {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16035;
        goto Errorhandling;
    }
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"ReadJsonOp", "");
    return  errorInfo;
}

/*ReadDeviceConfigJsonOp		   查找sqlite数据库text类型数据的函数
* @param[in]     name                   = 需要查找的记录名称
* @param[in]     version                = 需要查找的记录的版本号
* @return        " "                    = 查找记录中的内容为空
* @return        errorInfo.ErrCode非0   = 查找数据失败
* @return        content                = 查找到的内容数据
*/
ErrorInfo ReadDeviceConfigJsonOp(char* name, char* version, std::string* content)
{
    const void* pTest;
    char* select_sql;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    /*sqlite3* db;
    int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16036;
        goto Errorhandling;
    }*/
    sqlite3_stmt* stmt;
    select_sql = (char*)"SELECT Content FROM DeviceConfig WHERE Item=? AND Ver=?";
    rc = sqlite3_prepare_v2(db, select_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16037;
        goto Errorhandling;
    }
    sqlite3_bind_text(stmt, 1, name, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, version, -1, SQLITE_STATIC);
    rc = sqlite3_step(stmt);
    if (rc == SQLITE_ROW) {
        pTest = sqlite3_column_blob(stmt, 0);
        if (pTest != NULL)
        {
            *content = reinterpret_cast<const char*>(pTest);
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            goto Errorhandling;
        }
        else 
        {
            //printf("sqlite3_column_blob return  NULL\n");
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            *content= " ";
        }
    }
    else {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16038;
        goto Errorhandling;
    }
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"ReadDeviceConfigJsonOp", "");
    return  errorInfo;
}

/*DeleteJsonOp		           删除sqlite数据库记录的函数
* @param[in]     name                   = 需要删除的记录名称
* @param[in]     version                = 需要删除的记录的版本号
* @return        errorInfo.ErrCode非0   = 删除记录失败
* @return        errorInfo.ErrCode = 0  = 删除记录成功
*/
ErrorInfo DeleteJsonOp(char* name, char* version) {
    char* delete_sql;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    //sqlite3* db;
    char* err_msg = NULL;
    int rc;

    // 打开数据库连接
    /*rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16039;
        goto Errorhandling;
    }*/
    delete_sql = (char*)"DELETE FROM Sdo WHERE Name=? AND Version=?";
    sqlite3_stmt* stmt;
    rc = sqlite3_prepare_v2(db, delete_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16040;
        goto Errorhandling;
    }
    sqlite3_bind_text(stmt, 1, name, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, version, -1, SQLITE_STATIC);
    rc = sqlite3_step(stmt);
    if (rc != SQLITE_DONE) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        printf("删除记录失败: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16041;
        goto Errorhandling;
    }
    sqlite3_finalize(stmt);
    // 关闭数据库连接
    //sqlite3_close_v2(db);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"DeleteJsonOp", "");
    return  errorInfo;
}

/*WriteBlobOp		                插入二进制数据到数据库的函数
* @param[in]     name                   = 需要插入的记录名称
* @param[in]     content                = 需要插入的记录的内容
* @param[in]     size                   = 需要插入的二进制内容的大小
* @param[in]     version                = 需要插入的记录的版本号
* @return        errorInfo.ErrCode非0   = 插入记录失败
* @return        errorInfo.ErrCode = 0  = 插入记录成功
*/
ErrorInfo WriteBlobOp(char* name, void* content, int size, char* version){
    char* query_sql;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    int count = 0;
    //sqlite3* db;
    /*int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16042;
        goto Errorhandling;
    }*/
    query_sql = (char*)"INSERT OR REPLACE INTO Blob (Name,Content,Version) VALUES (?, ?, ?);";
    // 执行更新语句
    sqlite3_stmt* stmt;
    rc = sqlite3_prepare_v2(db, query_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16043;
        goto Errorhandling;
    }
    sqlite3_bind_blob(stmt, 2, content, size, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 1, name, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 3, version, -1, SQLITE_STATIC);
    rc = sqlite3_step(stmt);
    if (rc != SQLITE_DONE) {
        printf("更新记录失败: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16044;
        goto Errorhandling;
    }
    //rc = sqlite3_exec(db, "PRAGMA wal_checkpoint(TRUNCATE)", nullptr, nullptr, 0);
    sqlite3_finalize(stmt);
    // 关闭数据库连接
    //sqlite3_close_v2(db);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"WriteBlobOp", "");
    return  errorInfo;
}

/*DeleteBlobOp		                删除sqlite数据库记录的函数
* @param[in]     name                   = 需要删除的记录名称
* @param[in]     version                = 需要删除的记录的版本号
* @return        errorInfo.ErrCode非0   = 删除记录失败
* @return        errorInfo.ErrCode = 0  = 删除记录成功
*/
ErrorInfo DeleteBlobOp(char* name, char* version) {
    char* delete_sql;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    //sqlite3* db;
    char* err_msg = NULL;
    // 打开数据库连接
    /*int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16045;
        goto Errorhandling;
    }*/
    delete_sql = (char*)"DELETE FROM Blob WHERE Name=? AND Version=?";
    sqlite3_stmt* stmt;
    rc = sqlite3_prepare_v2(db, delete_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16046;
        goto Errorhandling;
    }
    sqlite3_bind_text(stmt, 1, name, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, version, -1, SQLITE_STATIC);
    rc = sqlite3_step(stmt);
    if (rc != SQLITE_DONE) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        printf("删除记录失败: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16047;
        goto Errorhandling;
    }
    sqlite3_finalize(stmt);
    // 关闭数据库连接
    //sqlite3_close_v2(db);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"DeleteBlobOp", "");
    return  errorInfo;
}

/*SelectBlobOp		                查找二进制数据的函数
* @param[in]     name                   = 需要查找的记录名称
* @param[in]     version                = 需要查找的记录的版本号
* @return        errorInfo.ErrCode非0   = 查找记录失败/内容为空
* @return        contentcopy           = 查找内容为空
* @return        contentcopy           = 查找记录的内容
*/
ErrorInfo SelectBlobOp(char* name, char* version, int* size, const void* contentcopy) {
    char* select_sql;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    //sqlite3* db;
    char* err_msg = NULL;
    // 打开数据库连接
    /*int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16048;
        goto Errorhandling;
    }*/
    select_sql = (char*)"SELECT Content FROM Blob WHERE Name=? AND Version=?";
    sqlite3_stmt* stmt;
    rc = sqlite3_prepare_v2(db, select_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16049;
        goto Errorhandling;
    }
    sqlite3_bind_text(stmt, 1, name, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, version, -1, SQLITE_STATIC);
    rc = sqlite3_step(stmt);
    if (rc == SQLITE_ROW) {
        contentcopy = sqlite3_column_blob(stmt, 0);
        if (contentcopy != NULL) {
            *size = sqlite3_column_bytes(stmt, 0);
            //contentcopy = malloc(*size);
            //memcpy(contentcopy, content, *size);
            //FILE* fp = fopen("/home/<USER>", "wb");
            //fwrite(contentcopy, *size, 1, fp);
            //fclose(fp);
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            goto Errorhandling;
        }
        else {
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            goto Errorhandling;
        }
    }
    else {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16050;
        goto Errorhandling;
    }
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"SelectBlobOp", "");
    return  errorInfo;
}

/*WriteResultConfigOp		        写入结果配置数据的函数
* @param[in]     Fid                    = 需要插入的profile的fid值
* @return        errorInfo.ErrCode非0   = 写入结果失败
* @return        errorInfo.ErrCode = 0  = 写入结果成功
*/
ErrorInfo WriteResultConfigOp(int Fid)
{
    const char* finish_sql;
    const char* update_sql;
    char* select_Device_sql;
    char* select_proflie_sql;
    //int rc_result;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    int i = 0;
    int count = 1;
    int Finishflag = 1;
    std::string str_array_md5name[100];
    std::string str_array_ver[100];
    std::string ProfileName;
    std::string ProfileVer;
    std::string UpdateTime;
    std::string GlobalVar;
    std::string FieldBus;
    std::string PartVer;
    std::string PartName;
    std::string ChartInfo;
    std::string DevBase;
    std::string BaseInfo;
    std::string Channel;
    std::string Calibration;
    const unsigned char* result;
    int RC_TRANSACTION;
    //sqlite3* db;
    //sqlite3* db_result;
    sqlite3_stmt* stmt;
    sqlite3_stmt* stmt_device;
    sqlite3_stmt* stmt_insert;
    sqlite3_stmt* stmt_finish;
    /*int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16051;
        goto Errorhandling;
    }
    rc_result = sqlite3_open_v2(DATABASE_RESULT, &db_result, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc_result != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db_result));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16052;
        goto Errorhandling;
    }*/
    select_proflie_sql = (char*)"SELECT Name ,Ver,UpdateTime FROM profile WHERE fid=?";
    select_Device_sql = (char*)"SELECT Ver,Md5code FROM DeviceConfig;";
    update_sql = "UPDATE Result SET ProfileUniquetime = ? ,FidName = ? ,ProfileVer = ?,\
        GlobalVar = ?  ,FieldBus = ?  ,IdGenerator = ?  ,\
        DataService = ?  ,ChartInfo = ? ,\
        DevBase = ? ,BaseInfo = ?,Channel = ? ,Calibration = ?\
        WHERE Finishflag= 0 ; ";
    finish_sql = "UPDATE Result SET Finishflag = ? WHERE Finishflag= 0;";
    rc = sqlite3_prepare_v2(db, select_proflie_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        //sqlite3_close_v2(db_result);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16053;
        goto Errorhandling;
    }
    else {
        rc = sqlite3_bind_int(stmt, 1, Fid);
        if (rc == SQLITE_OK) {
            rc = sqlite3_step(stmt);
            if (rc == SQLITE_ROW) {
                ProfileName = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 0));
                ProfileVer = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
                UpdateTime = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2));
                sqlite3_finalize(stmt);
            }
            else {
                sqlite3_finalize(stmt);
                //sqlite3_close_v2(db);
                //sqlite3_close_v2(db_result);
                errorInfo.ErrCode = 16144;
                errorInfo.eErrLever = Error;
                goto Errorhandling;
            }
        }
        else {
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            //sqlite3_close_v2(db_result);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16057;
            goto Errorhandling;
        }
    }
    rc = sqlite3_prepare_v2(db, select_Device_sql, -1, &stmt_device, NULL);
    if (rc != SQLITE_OK) {
        //sqlite3_finalize(stmt);
        sqlite3_finalize(stmt_device);
        //sqlite3_close_v2(db);
        //sqlite3_close_v2(db_result);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16054;
        goto Errorhandling;
    }
    // 开始一个事务
    RC_TRANSACTION = sqlite3_exec(db_result, "BEGIN TRANSACTION;", nullptr, nullptr, nullptr);
    if (RC_TRANSACTION != SQLITE_OK) {
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16138;
        goto Errorhandling;
    }
    rc_result = sqlite3_prepare_v2(db_result, update_sql, -1, &stmt_insert, NULL);
    if (rc_result != SQLITE_OK) {
        printf("sql语句语法错误: %s\n", sqlite3_errmsg(db_result));
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        //sqlite3_finalize(stmt);
        sqlite3_finalize(stmt_device);
        sqlite3_finalize(stmt_insert);
        //sqlite3_close_v2(db);
        //sqlite3_close_v2(db_result);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16055;
        goto Errorhandling;
    }
    rc_result = sqlite3_prepare_v2(db_result, finish_sql, -1, &stmt_finish, NULL);
    if (rc_result != SQLITE_OK) {
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        //sqlite3_finalize(stmt);
        sqlite3_finalize(stmt_device);
        sqlite3_finalize(stmt_insert);
        sqlite3_finalize(stmt_finish);
        //sqlite3_close_v2(db);
        //sqlite3_close_v2(db_result);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16056;
        goto Errorhandling;
    }
    
    while ((rc = sqlite3_step(stmt_device)) == SQLITE_ROW) {
        i++;
        str_array_ver[i] = reinterpret_cast<const char*>(sqlite3_column_text(stmt_device, 0));
        result = sqlite3_column_text(stmt_device, 1);
        if (result != NULL) {
            str_array_md5name[i] = reinterpret_cast<const char*>(result);
        }
        else {
            str_array_md5name[i] = "0";
        }
        rc_result = sqlite3_bind_text(stmt_insert, 3 + i, str_array_md5name[i].c_str(), -1, SQLITE_STATIC);
        //rc = sqlite3_bind_text(stmt_insert, 8 + count, str_array_ver[i].c_str(), -1, SQLITE_STATIC);
        if (rc_result != SQLITE_OK){
            sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
            //sqlite3_finalize(stmt);
            sqlite3_finalize(stmt_device);
            sqlite3_finalize(stmt_insert);
            sqlite3_finalize(stmt_finish);
            //sqlite3_close_v2(db);
            //sqlite3_close_v2(db_result);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16058;
            goto Errorhandling;
        }
        //count += 1;
    }
    if (rc_result == SQLITE_OK) {
        //rc_result = sqlite3_bind_int(stmt_insert, 13, Fid);
        rc_result += sqlite3_bind_text(stmt_insert, 1, UpdateTime.c_str(), -1, SQLITE_STATIC);
        rc_result += sqlite3_bind_text(stmt_insert, 2, ProfileName.c_str(), -1, SQLITE_STATIC);
        rc_result += sqlite3_bind_text(stmt_insert, 3, ProfileVer.c_str(), -1, SQLITE_STATIC);
        /*rc_result += sqlite3_bind_text(stmt_insert, 4, OverviewInformation, -1, SQLITE_STATIC);
        rc_result += sqlite3_bind_text(stmt_insert, 5, OverviewInformationVer, -1, SQLITE_STATIC);
        rc_result += sqlite3_bind_text(stmt_insert, 6, CurveResult, -1, SQLITE_STATIC);
        rc_result += sqlite3_bind_text(stmt_insert, 7, CurveResultVer, -1, SQLITE_STATIC);*/
    }
    else {
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        //sqlite3_finalize(stmt);
        sqlite3_finalize(stmt_device);
        sqlite3_finalize(stmt_insert);
        sqlite3_finalize(stmt_finish);
        //sqlite3_close_v2(db);
        //sqlite3_close_v2(db_result);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16059;
        goto Errorhandling;
    }
    if (rc_result == SQLITE_OK) {
        rc_result = sqlite3_step(stmt_insert);
    }
    else {
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        //sqlite3_finalize(stmt);
        sqlite3_finalize(stmt_device);
        sqlite3_finalize(stmt_insert);
        sqlite3_finalize(stmt_finish);
        //sqlite3_close_v2(db);
        //sqlite3_close_v2(db_result);
        //std::cout << sqlite3_errmsg(db_result) << std::endl;
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16060;
        goto Errorhandling;
    }
    if (rc_result == SQLITE_DONE) {
        rc_result = sqlite3_bind_int(stmt_finish, 1, Finishflag);
        if (rc_result == SQLITE_OK) {
            rc_result = sqlite3_step(stmt_finish);
        }
        else {
            sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
            //sqlite3_finalize(stmt);
            sqlite3_finalize(stmt_device);
            sqlite3_finalize(stmt_insert);
            sqlite3_finalize(stmt_finish);
            //sqlite3_close_v2(db);
            //sqlite3_close_v2(db_result);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16061;
            goto Errorhandling;
        }
        if (rc_result == SQLITE_DONE) {
            // 提交事务
            rc_result = sqlite3_exec(db_result, "COMMIT;", nullptr, nullptr, nullptr);
            if (rc_result != SQLITE_OK) {
                errorInfo.eErrLever = Error;
                errorInfo.ErrCode = 16139;
                ErrorInfoPack(&errorInfo, (char*)"WriteJsonResultOp", "");
                return errorInfo;
            }
            //sqlite3_finalize(stmt);
            sqlite3_finalize(stmt_device);
            sqlite3_finalize(stmt_insert);
            sqlite3_finalize(stmt_finish);
            errorInfo = checkAndCheckpointWal(db_result,"/userdata/resultDb.lx-wal",100 * 1024 * 1024,"resultDb");
            if (errorInfo.ErrCode != 0) {
                goto Errorhandling;
            }
            errorInfo = checkAndCheckpointWal(db,"/root/press.lx-wal",50 * 1024 * 1024,"press");
            if (errorInfo.ErrCode != 0) {
                goto Errorhandling;
            }
            //sqlite3_close_v2(db);
            //sqlite3_close_v2(db_result);
            goto Errorhandling;
        }
        else {
            sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
            //sqlite3_finalize(stmt);
            sqlite3_finalize(stmt_device);
            sqlite3_finalize(stmt_insert);
            sqlite3_finalize(stmt_finish);
            //sqlite3_close_v2(db);
            //sqlite3_close_v2(db_result);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16062;
            goto Errorhandling;
        }
    }
    else {
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        //sqlite3_finalize(stmt);
        sqlite3_finalize(stmt_device);
        sqlite3_finalize(stmt_insert);
        sqlite3_finalize(stmt_finish);
        //sqlite3_close_v2(db);
        //sqlite3_close_v2(db_result);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16063;
    }
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"WriteResultConfigOp", "");
    return  errorInfo;
}

/*WriteBlobResultOp		            写入EO结果数据的函数
* @param[in]     Fid                    = 需要插入的profile的fid值
* @param[in]     Charts                 = 需要插入的二进制结果数据
* @param[in]     size                   = 需要插入的二进制结果数据的大小
* @param[in]     ChartsVer              = 需要插入的二进制结果的版本数据
* @return        errorInfo.ErrCode非0   = 写入结果失败
* @return        errorInfo.ErrCode = 0  = 写入结果成功
*/
ErrorInfo WriteBlobResultOp(int Fid , void* Charts,int size, char* ChartsVer)
{
    int changes_count;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    char* query_sql_step;
    char* query_sql;
    int count = 0;
    /*sqlite3* db_result;
    int rc = sqlite3_open_v2(DATABASE_RESULT, &db_result, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db_result));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16064;
        goto Errorhandling;
    }*/
    //auto query_sql = "BEGIN; IF EXISTS(SELECT 1 FROM Result WHERE Finishflag = 0) THEN UPDATE Result SET Fid = ? , Charts = ? WHERE Finishflag = 0; ELSE INSERT INTO Result(Fid, Charts) VALUES(? , ? ); END IF; END; ";
    //query_sql = "INSERT INTO Result(Fid, Charts,ChartsVer) SELECT ? , ? ,? WHERE NOT EXISTS(SELECT 1 FROM Result WHERE Finishflag = 0); ";
    query_sql = (char*)"INSERT INTO Result() SELECT ? , ? ,? ; ";
    query_sql_step = (char*)" UPDATE Result SET  Charts = ? ,ChartsVer = ? WHERE Finishflag = 0 AND  Fid = ?;";
    sqlite3_stmt* stmt;
    sqlite3_stmt* stmt_step;
    //sqlite3_exec(db_result, "PRAGMA synchronous = OFF;", nullptr, nullptr, nullptr);

    //// 开始一个事务
    int RC_TRANSACTION = sqlite3_exec(db_result, "BEGIN TRANSACTION;", nullptr, nullptr, nullptr);
    if (RC_TRANSACTION!= SQLITE_OK) {
        printf(sqlite3_errmsg(db_result));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16140;
        goto Errorhandling;
    }
    rc_result = sqlite3_prepare_v2(db_result, query_sql, -1, &stmt, NULL);
    if (rc_result != SQLITE_OK) {
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16065;
        goto Errorhandling;
    }
    rc_result = sqlite3_prepare_v2(db_result, query_sql_step, -1, &stmt_step, NULL);
    if (rc_result != SQLITE_OK) {
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        sqlite3_finalize(stmt_step);
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16066;
        goto Errorhandling;
    }
    rc_result = sqlite3_bind_int(stmt, 1, Fid);
    rc_result += sqlite3_bind_blob(stmt, 2, Charts, size, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt, 3, ChartsVer, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_int(stmt_step, 3, Fid);
    rc_result += sqlite3_bind_text(stmt_step, 2, ChartsVer, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_blob(stmt_step, 1, Charts, size, SQLITE_STATIC);
    if (rc_result == SQLITE_OK) {
        rc_result = sqlite3_step(stmt);
    }
    else {
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        sqlite3_finalize(stmt_step);
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16067;
        goto Errorhandling;
    }
    changes_count = sqlite3_changes(db_result);
    if (changes_count == 0) {
        rc_result = sqlite3_step(stmt_step);
    }
    //sqlite3_exec(db_result, "COMMIT;", nullptr, nullptr, nullptr);
    if (rc_result == SQLITE_DONE) {
        // 提交事务
        rc_result = sqlite3_exec(db_result, "COMMIT;", nullptr, nullptr, nullptr);
        if (rc_result != SQLITE_OK) {
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16141;
            ErrorInfoPack(&errorInfo, (char*)"WriteJsonResultOp", "");
            return errorInfo;
        }
        sqlite3_finalize(stmt);
        sqlite3_finalize(stmt_step);
        //sqlite3_close_v2(db);
        goto Errorhandling;
    }
    else {
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        sqlite3_finalize(stmt);
        sqlite3_finalize(stmt_step);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16068;
    }
    //sqlite3_close_v2(db_result);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"WriteBlobResultOp", "");
    return  errorInfo;
}

bool is_valid_json_rapidjson(const char* str) {
    if (!str || *str == '\0') { // Treat null or empty string as invalid input for parsing
        return false;
    }

    rapidjson::Document doc;
    // Parse the string. This overload expects a null-terminated string.
    // If you cannot guarantee null termination for char*, consider using Parse(str, len).
    doc.Parse(str);

    if (doc.HasParseError()) {
        // Optional: Log the parse error
        // fprintf(stderr, "RapidJSON parse error for char* at offset %zu: %s\n",
        //         doc.GetErrorOffset(),
        //         rapidjson::GetParseError_En(doc.GetParseError()));
        return false; // Parsing failed, not valid JSON
    }

    return true; // Parsing succeeded, it's valid JSON
}

// Helper function to validate JSON using RapidJSON (for std::string)
// Returns true if the string is valid JSON, false otherwise.
bool is_valid_json_rapidjson(const std::string& str) {
    if (str.empty()) { // Treat empty string as invalid input
        return false;
    }

    rapidjson::Document doc;
    // Parse the string using c_str() and length(). This is generally safer for std::string.
    doc.Parse(str.c_str(), str.length());

    if (doc.HasParseError()) {
        // Optional: Log the parse error
        // fprintf(stderr, "RapidJSON parse error for std::string at offset %zu: %s\n",
        //         doc.GetErrorOffset(),
        //         rapidjson::GetParseError_En(doc.GetParseError()));
        return false; // Parsing failed, not valid JSON
    }

    return true; // Parsing succeeded, it's valid JSON
}

/*WriteJsonResultOp		            写入EO结果数据的函数
* @param[in]     Fid                    = 需要插入的profile的fid值
* @param[in]     content                = 需要插入的Eo结果数据
* @param[in]     EoResultVer            = 需要插入的Eo版本数据
* @return        errorInfo.ErrCode非0   = 写入结果失败
* @return        errorInfo.ErrCode = 0  = 写入结果成功
*/
ErrorInfo WriteJsonResultOp(int Fid, char* EoResult,char* EoResultVer,char* OverviewInformation,char* OverviewInformationVer, char* CurveResult, char* CurveResultVer, std::string RefConf, std::string RefResult,void* Charts, int size,char* ChartsVer)
{
    //ErrorInfo ret;
    //ret.ErrCode = 0;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    char* query_sql;
    char* query_sql_step;
    int changes_count;
    //ErrorInfo errorInfo;
    //errorInfo.ErrCode = 0;
    int count = 0;
    int RC_TRANSACTION;
    /*sqlite3* db;
    int rc = sqlite3_open_v2(DATABASE_RESULT, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16069;
        goto Errorhandling;
    }*/
    if (!is_valid_json_rapidjson(EoResult)) {
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16205; // Custom error code for JSON validation failure
        // fprintf(stderr, "JSON Validation failed for EoResult (Fid: %d).\n", Fid);
        goto Errorhandling; // Skip DB operations and go to cleanup/exit
    }

    if (!is_valid_json_rapidjson(OverviewInformation)) {
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16205; // Same error code for any JSON validation failure
        // fprintf(stderr, "JSON Validation failed for OverviewInformation (Fid: %d).\n", Fid);
        goto Errorhandling;
    }

    if (!is_valid_json_rapidjson(CurveResult)) {
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16205;
        // fprintf(stderr, "JSON Validation failed for CurveResult (Fid: %d).\n", Fid);
        goto Errorhandling;
    }

    if (!is_valid_json_rapidjson(RefConf)) {
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16205;
        // fprintf(stderr, "JSON Validation failed for RefConf (Fid: %d).\n", Fid);
        goto Errorhandling;
    }

    if (!is_valid_json_rapidjson(RefResult)) {
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16205;
        // fprintf(stderr, "JSON Validation failed for RefResult (Fid: %d).\n", Fid);
        goto Errorhandling;
    }

    query_sql = (char*)"INSERT INTO Result(Fid, EoResult,EoResultVer,OverviewInformation,OverviewInformationVer,CurveResult,CurveResultVer,RefConf,RefResult,Charts,ChartsVer) SELECT ?,?,?,?,?,?,?,?,?,?,? WHERE NOT EXISTS(SELECT 1 FROM Result WHERE Finishflag = 0); ";
    //query_sql_step = " UPDATE Result SET  EoResult = ? ,EoResultVer =? ,OverviewInformation = ? ,OverviewInformationVer = ?,CurveResult = ?,CurveResultVer = ?, RefConf = ?,RefResult = ? WHERE Finishflag = 0 ;";
    sqlite3_stmt* stmt;
    //sqlite3_stmt* stmt_step;
    RC_TRANSACTION = sqlite3_exec(db_result, "BEGIN TRANSACTION;", nullptr, nullptr, nullptr);
    if (RC_TRANSACTION != SQLITE_OK) {
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16142;
        goto Errorhandling;
    }
    rc_result = sqlite3_prepare_v2(db_result, query_sql, -1, &stmt, NULL);
    //rc_result += sqlite3_prepare_v2(db_result, query_sql_step, -1, &stmt_step, NULL);
    rc_result += sqlite3_bind_int(stmt, 1, Fid);
    rc_result += sqlite3_bind_text(stmt, 2, EoResult, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt, 3, EoResultVer, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt, 4, OverviewInformation, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt, 5, OverviewInformationVer, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt, 6, CurveResult, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt, 7, CurveResultVer, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt, 8, RefConf.c_str(), -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt, 9, RefResult.c_str(), -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_blob(stmt, 10, Charts, size, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt, 11, ChartsVer, -1, SQLITE_STATIC);
    //rc_result += sqlite3_bind_int(stmt_step, 9, Fid);
    /*rc_result += sqlite3_bind_text(stmt_step, 1, EoResult, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt_step, 2, EoResultVer, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt_step, 3, OverviewInformation, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt_step, 4, OverviewInformationVer, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt_step, 5, CurveResult, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt_step, 6, CurveResultVer, -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt_step, 7, RefConf.c_str(), -1, SQLITE_STATIC);
    rc_result += sqlite3_bind_text(stmt_step, 8, RefResult.c_str(), -1, SQLITE_STATIC);*/
    if (rc_result == SQLITE_OK) {
        rc_result = sqlite3_step(stmt);
    }
    else {
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        sqlite3_finalize(stmt);
        //sqlite3_finalize(stmt_step);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16070;
        goto Errorhandling;
    }
    /*changes_count = sqlite3_changes(db_result);
    if (changes_count == 0) {
        rc_result = sqlite3_step(stmt_step);
    }*/
    if (rc_result == SQLITE_DONE) {
        rc_result = sqlite3_exec(db_result, "COMMIT;", nullptr, nullptr, nullptr);
        if (rc_result != SQLITE_OK) {
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16143;
            ErrorInfoPack(&errorInfo, (char*)"WriteJsonResultOp", "");
            return errorInfo;
        }
        sqlite3_finalize(stmt);
        //sqlite3_finalize(stmt_step);
        //sqlite3_close_v2(db);
        errorInfo = WriteResultConfigOp(Fid);
        if (errorInfo.ErrCode == 0) {
            goto Errorhandling;
        }
        else {
            sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16071;
            goto Errorhandling;
        }
    }
    else {
        sqlite3_exec(db_result, "ROLLBACK;", nullptr, nullptr, nullptr);
        sqlite3_finalize(stmt);
        //sqlite3_finalize(stmt_step);
        //sqlite3_close_v2(db);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16072;
    }
    Errorhandling:
    /*if (errorInfo.ErrCode)
    {
        ErrorInfoPack(&errorInfo, "UpdateProfile");
    }
    if (ret.ErrCode != 0) {
        strcat(errorInfo.sErrDescribe, ": ");
        strcat(errorInfo.sErrDescribe, ret.sErrDescribe);
    }
    return errorInfo;*/
    ErrorInfoPack(&errorInfo, (char*)"WriteJsonResultOp", "");
    return  errorInfo;

}

/*isCIFSMounted		                    判断是否挂载成功的函数
* @param[in]     ip                     = 远程数据服务器的ip
* @param[in]     dir                    = 远程数据服务器的共享文件夹路径
* @return        false                  = 挂载失败
* @return        true                   = 挂载成功
*/
bool isCIFSMounted(const std::string& ip, const std::string& dir) {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    std::ifstream mtab("/proc/mounts");
    std::string line;
    while (std::getline(mtab, line)) {
        if (line.find("//" + ip + "/" + dir) != std::string::npos) {
            return true;
        }
    }
    return false;
}

/*mountCIFSShare		                挂载共享文件夹的函数
* @param[in]     ip                     = 远程数据服务器的ip
* @param[in]     Directory              = 远程数据服务器的共享文件夹路径
* @param[in]     User                   = 远程数据服务器的用户名
* @param[in]     Password               = 远程数据服务器的主机密码
* @return        errorInfo.ErrCode非0   = 挂载失败
* @return        errorInfo.ErrCode = 0  = 挂载成功
*/
ErrorInfo mountCIFSShare(std::string ip, std::string Directory, std::string User, std::string Password, bool* pbConnectTest)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    int ret = 2;
    char cmd[1024];
    char checkCmd[512];
    //sprintf(checkCmd, "grep -q '^//%s/%s ' /proc/mounts", ip.c_str(), Directory.c_str());
    if (isCIFSMounted(ip, Directory)) {
        std::cout << "Directory is already mounted." << std::endl;
    }
    else
    {
        sprintf(cmd, "mount -t cifs -o username=%s,password=%s,soft,actimeo=3,echo_interval=5 //%s/%s /userdata/dir_test/", User.c_str(), Password.c_str(), ip.c_str(), Directory.c_str());
        std::cout << cmd << std::endl;
        ret = system(cmd);
        if (ret != 0) {
            if (*pbConnectTest) {
                *pbConnectTest = false;
            }
            std::cerr << "挂载Windows共享文件夹失败" << std::endl;
            errorInfoapi.eErrLever = Warm;
            errorInfo.ErrCode = 16091;
            goto Errorhandling;
        }
    }
    // 执行挂载命令
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"dataServer", "");
    return  errorInfo;
}

/*rulesHandle		                    判断指定规则的文件夹路径
* @param[in]     relus                  = 用户指定规则
* @param[in]     timestamp              = 生成结果的时间戳
* @param[in]     is_pass                = 生成结果是否成功的标志位
* @return        path                   = 需要生成的路径
*/
std::string rulesHandle(char* relus, char* timestamp, int is_pass)
{
    std::string path;
    if (strcmp(relus, "root") == 0) {
        path += "";
    }
    else {
        int i = 0;
        char  str[] = "ab-cd-ef";
        char* relus_list;
        char* relus_list_finl;
        char* times_Year;
        char* times_Month;
        char* times_Day;
        std::string temp_path;
        char* times_list;
        char* p = NULL;
        char* t = NULL;
        char* s = NULL;
        char timestampcopy[128];
        char reluscopy[128];
        strcpy(timestampcopy, timestamp);
        strcpy(reluscopy, relus);
        times_Year = strtok(timestampcopy, "-");
        times_Month = strtok(NULL, "-");
        times_Day = strtok(NULL, "-");
        relus_list = strtok(reluscopy, "\\");
        relus_list_finl = strtok(NULL, "\\");
        if (relus_list_finl == NULL) {
            relus_list_finl = (char*)"1";
        }
        if (strcmp(relus_list, "NOK") != 0) {
            relus_list = strtok(relus_list, "-");
            while (relus_list != NULL)
            {
                if (strcmp(relus_list, "YYYY") == 0) {
                    temp_path += times_Year;
                }
                else if (strcmp(relus_list, "MM") == 0) {
                    temp_path += "-";
                    temp_path += times_Month;
                }
                else if (strcmp(relus_list, "DD") == 0) {
                    temp_path += "-";
                    temp_path += times_Day;
                }
                relus_list = strtok(NULL, "-");
            }
        }
        else {
            if (is_pass) {
                temp_path += "OK";
            }
            else {
                temp_path += "NOK";
            }

        }
        if (relus_list_finl != NULL) {
            temp_path += "/";
        }
        if (strcmp(relus_list_finl, "NOK") != 0 && relus_list_finl != NULL) {
            relus_list = strtok(relus_list_finl, "-");
            while (relus_list != NULL)
            {
                if (strcmp(relus_list, "YYYY") == 0) {
                    temp_path += times_Year;
                }
                else if (strcmp(relus_list, "MM") == 0) {
                    temp_path += "-";
                    temp_path += times_Month;
                }
                else if (strcmp(relus_list, "DD") == 0) {
                    temp_path += "-";
                    temp_path += times_Day;
                }
                relus_list = strtok(NULL, "-");
            }
        }
        else if (strcmp(relus_list_finl, "NOK") == 0 && relus_list_finl != NULL) {
            if (is_pass) {
                temp_path += "OK";
            }
            else {
                temp_path += "NOK";
            }
        }
        return temp_path;
    }
    return path;
}

/*mkdirRecursive		                生成指定规则的文件夹路径
* @param[in]     path                   = 需要生成的路径
* @return        errorInfo.ErrCode非0   = 生成路径失败
* @return        errorInfo.ErrCode = 0  = 生成路径成功
*/
ErrorInfo mkdirRecursive(const char* path)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    char tmp[256];
    char* p = NULL;
    size_t len;

    snprintf(tmp, sizeof(tmp), "%s", path);

    len = strlen(tmp);
    if (tmp[len - 1] == '/')
        tmp[len - 1] = 0;

    for (p = tmp + 1; *p; p++) {
        if (*p == '/') {
            *p = 0;
            if (mkdir(tmp, S_IRWXU) != 0) {
                if (errno != EEXIST) {
                    fprintf(stderr, "[ERROR] 创建目录失败: %s (错误码: %d)\n", strerror(errno), errno);
                    errorInfo.ErrCode = 16100;
                    goto Errorhandling;
                }
            }
            *p = '/';
        }
    }
    if (mkdir(tmp, S_IRWXU) != 0) {
        if (errno != EEXIST) {
            errorInfo.ErrCode = 16099;
            goto Errorhandling;
        }
    }
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"dataServer", "");
    return  errorInfo;
}


// Function to convert binary data to float
float BinaryToFloat(const void* data) {
    float result;
    std::memcpy(&result, data, sizeof(float));
    return result;
}

/*resultDirDeal		                    生成指定规则的文件夹路径
* @param[in]     db                     = 结果数据库服务器
* @param[in]     PathRule               = 用户指定路径规则
* @return        errorInfo.ErrCode非0   = 处理结果失败
* @return        errorInfo.ErrCode = 0  = 处理结果成功
*/
ErrorInfo resultDirDeal(sqlite3* dbpress, sqlite3* db, char* PathRule)
{   
    std::lock_guard<std::mutex> lock(g_database_mutex);
    auto getStringOrDefault = [](const rapidjson::Value& json, const char* field, const char* defaultValue) {
        return json.HasMember(field) && json[field].IsString() ? json[field].GetString() : defaultValue;
        };

    auto getIntOrDefault = [](const rapidjson::Value& json, const char* field, int defaultValue) {
        return json.HasMember(field) && json[field].IsInt() ? json[field].GetInt() : defaultValue;
        };

    auto getBoolOrDefault = [](const rapidjson::Value& json, const char* field, bool defaultValue) {
        return json.HasMember(field) && json[field].IsBool() ? json[field].GetBool() : defaultValue;
        };

    auto getDoubleOrDefault = [](const rapidjson::Value& json, const char* field, double defaultValue) {
        if (json.HasMember(field)) {
            if (json[field].IsDouble()) {
                return json[field].GetDouble();
            }
            else if (json[field].IsInt()) {
                return static_cast<double>(json[field].GetInt());
            }
        }
        return defaultValue;
        };

    auto getFloatOrDefault = [](const rapidjson::Value& json, const char* field, float defaultValue) {
        if (json.HasMember(field)) {
            if (json[field].IsFloat()) {
                return json[field].GetFloat();
            }
            else if (json[field].IsInt()) {
                return static_cast<float>(json[field].GetInt());
            }
        }
        return defaultValue;
        };

    std::map<std::string, std::map<std::string, int>> rateMap = {
        {"RealTime", {{"10%", 1}, {"20%", 2}, {"50%", 3}}},
        {"TimePeak", {{"10%", 1}, {"20%", 2}, {"50%", 3}}},
        {"EqualPos", {{"0.01mm", 1}, {"0.1mm", 2}, {"1mm", 3}}},
        {"EqualForce", {{"0.01KN", 1}, {"0.1KN", 2}, {"1KN", 3}}}
    };

    std::map<std::string, int> modeMap = {
    {"RealTime", 1},
    {"TimePeak", 2},
    {"EqualPos", 3},
    {"EqualForce", 4}
    };
    rapidjson::Document json;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    rapidjson::Document eoResultjson;
    rapidjson::Document json_reqfinal;
    const char* refresult;
    const char* refconf;
    const char* OverviewInformationver;
    const char* OverviewInformation;
    std::string eocontent;
    //const char* Channelcontent;
    const char* ChartInfocontent;
    std::string Creationtime_tmp;
    std::string partId = "initial_value";
    std::string eoResultfinal;
    std::string ProfileUniquetime;
    std::string Channel;
    std::string eomd5code;
    std::string ChartInfo;
    std::string Channelcontent;
    char* force_unit[] = { (char*)"kN", (char*)"N", "kgf" };
    char* position_unit[] = { (char*)"mm", (char*)"m", (char*)"inch" };
    std::string Path_temp = "/userdata/dir_test/";
    sqlite3_stmt* stmt;
    sqlite3_stmt* stmtselectpf;
    sqlite3_stmt* stmtselecteoct;
    sqlite3_stmt* stmtselectChannel;
    std::string sql = "SELECT FidName, EoResult, Charts, Creationtime, OverviewInformation, OverviewInformationVer, RefConf, RefResult,Id ,ProfileUniquetime,Channel,ChartInfo FROM Result WHERE Id = ? AND Finishflag = 1;";
    //int ret = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    std::string sqlselectmd5 = "SELECT EO FROM profilerecord WHERE uniqueid = ?";
    std::string sqlselecteoct = "SELECT content FROM profilemd5 WHERE md5code = ?";
    std::string sqlselectChannel = "SELECT content FROM DeviceConfigMd5 WHERE md5code = ?";
    int ret = sqlite3_prepare_v3(db, sql.c_str(), sql.length(), 0, &stmt, NULL);
    ret += sqlite3_prepare_v3(dbpress, sqlselectmd5.c_str(), sqlselectmd5.length(), 0, &stmtselectpf, NULL);
    ret += sqlite3_prepare_v3(dbpress, sqlselecteoct.c_str(), sqlselecteoct.length(), 0, &stmtselecteoct, NULL);
    ret += sqlite3_prepare_v3(dbpress, sqlselectChannel.c_str(), sqlselectChannel.length(), 0, &stmtselectChannel, NULL);
    if (ret != SQLITE_OK) {
        std::cerr << "Failed to query: " << sqlite3_errmsg(db) << std::endl;
        errorInfoapi.eErrLever = Warm;
        errorInfo.ErrCode = 16092;
        goto Errorhandling;
    }
    for (int i = 0; i < 1; ++i) {
        int rc = sqlite3_bind_int(stmt, 1,MaxId);
        if (rc == SQLITE_OK) {
            rc = sqlite3_step(stmt);
            if (rc == SQLITE_ROW) {
                MaxId++;
                const unsigned char* Channeltmp = sqlite3_column_text(stmt, 10);
                const unsigned char* ChartInfotmp = sqlite3_column_text(stmt, 11);
                const unsigned char* ProfileUniquetimetmp = sqlite3_column_text(stmt, 9);
                const unsigned char* resulttime = sqlite3_column_text(stmt, 3);
                const unsigned char* eoResult = sqlite3_column_text(stmt, 1);
                //std::string result(reinterpret_cast<const char*>(eoResult));
                //std::cout << result << std::endl;
                if (ChartInfotmp != NULL) {
                    ChartInfo = reinterpret_cast<const char*>(ChartInfotmp);
                }
                else {
                    ChartInfo = " ";
                }

                if (ProfileUniquetimetmp != NULL) {
                    ProfileUniquetime = reinterpret_cast<const char*>(ProfileUniquetimetmp);
                }
                else {
                    ProfileUniquetime = " ";
                }
                if (Channeltmp != NULL) {
                    Channel = reinterpret_cast<const char*>(Channeltmp);
                }
                else {
                    Channel = " ";
                }
                ret = sqlite3_bind_text(stmtselectpf, 1, ProfileUniquetime.c_str(), -1, SQLITE_STATIC);
                ret += sqlite3_bind_text(stmtselectChannel, 1, Channel.c_str(), -1, SQLITE_STATIC);
                if (ret == SQLITE_OK) {
                    int retChannel = sqlite3_step(stmtselectChannel);
                    if (retChannel == SQLITE_ROW) {
                        const unsigned char* Channelcontenttmp = sqlite3_column_text(stmtselectChannel, 0);
                        if (Channelcontenttmp != NULL) {
                            Channelcontent = reinterpret_cast<const char*>(Channelcontenttmp);
                        }
                        else {
                            Channelcontent = " ";
                        }
                    }
                    else {
                        sqlite3_reset(stmtselecteoct);
                        errorInfo.ErrCode = 16094;
                        goto Errorhandling;
                    }
                    ret = sqlite3_step(stmtselectpf);
                    if (ret == SQLITE_ROW) {
                        const unsigned char* eomd5 = sqlite3_column_text(stmtselectpf, 0);
                        if (eomd5 != NULL) {
                            eomd5code = reinterpret_cast<const char*>(eomd5);
                        }
                        else {
                            eomd5code = " ";
                        }
                        sqlite3_reset(stmtselectpf);
                        ret = sqlite3_bind_text(stmtselecteoct, 1, eomd5code.c_str(), -1, SQLITE_STATIC);
                        if (ret == SQLITE_OK) {
                            ret = sqlite3_step(stmtselecteoct);
                            if (ret == SQLITE_ROW) {
                                const unsigned char* eocontenttmp = sqlite3_column_text(stmtselecteoct, 0);
                                if (eocontenttmp != NULL) {
                                    eocontent = std::string(reinterpret_cast<const char*>(eocontenttmp));
                                }
                                else {
                                    eocontent = " ";
                                }
                            }
                            sqlite3_reset(stmtselecteoct);
                        }
                        else {
                            sqlite3_reset(stmtselecteoct);
                            errorInfoapi.eErrLever = Warm;
                            errorInfo.ErrCode = 16095;
                            goto Errorhandling;
                        }
                    }
                    else {
                        sqlite3_reset(stmtselectpf);
                    }
                }
                else {
                    sqlite3_reset(stmtselectpf);
                    errorInfoapi.eErrLever = Warm;
                    errorInfo.ErrCode = 16093;
                    goto Errorhandling;
                }
                sqlite3_reset(stmtselectChannel);

                if (resulttime != NULL) {
                    Creationtime_tmp = reinterpret_cast<const char*>(resulttime);
                }
                else {
                    Creationtime_tmp = " ";
                }
                sqlite3_int64 rowid = sqlite3_column_int64(stmt, 8);
                // 找到第一个空格,以此将字符串切分为日期和时间
                size_t space_pos = Creationtime_tmp.find(' ');
                std::string date = Creationtime_tmp.substr(0, space_pos);
                std::string time = Creationtime_tmp.substr(space_pos + 1);
                const char* Creationtime = date.c_str();
                // 解析 EoResult 字段的 JSON 数据
                if (eoResult != NULL) {
                    eoResultfinal = reinterpret_cast<const char*>(eoResult);
                }
                else {
                    eoResultfinal = " ";
                }
                if (eoResultjson.Parse(eoResultfinal.c_str()).HasParseError()) {
                    std::cerr << "JSON parse error: " << json.GetParseError() << std::endl;
                    errorInfoapi.eErrLever = Warm;
                    errorInfo.ErrCode = 16131;
                    goto Errorhandling;
                
                };
                // 根据 CraftIsOK 的值决定传递给 rulesHandle 的参数
                int passParam = 0;
                if (eoResultjson.IsNull() || !eoResultjson.IsObject()) {
                    passParam = 0; // 使用默认值
                }
                else {
                    passParam = getBoolOrDefault(eoResultjson, "CraftIsOK", 0) ? 1 : 0;
                }
                std::string Path;
                std::string final_path;
                try {
                    Path = rulesHandle(PathRule, const_cast<char*>(Creationtime), passParam);
                    final_path = Path_temp + Path;
                }
                catch (const std::exception& e) {
                    std::cerr << "Exception caught: " << e.what() << std::endl;
                    final_path = Path_temp;
                }
                struct stat st;
                if (stat(final_path.c_str(), &st) == -1) {
                    try {
                        errorInfo = mkdirRecursive(final_path.c_str());
                        if (errorInfo.ErrCode != 0) {
                            errorInfoapi.eErrLever = Warm;
                            errorInfo.ErrCode = 16097;
                            goto Errorhandling;
                        }
                    }
                    catch (const std::exception& e) {
                        std::cerr << "Exception caught: " << e.what() << std::endl;
                        errorInfoapi.eErrLever = Warm;
                        errorInfo.ErrCode = 16097;
                        goto Errorhandling;
                    }
                }
                // 读取OverviewInformation字段中的PartId内容
                const unsigned char* resultOverviewInformation = sqlite3_column_text(stmt, 4);
                if (resultOverviewInformation != NULL) {
                    OverviewInformation = reinterpret_cast<const char*>(resultOverviewInformation);
                }
                else {
                    OverviewInformation = " ";
                }
                if (json_reqfinal.Parse(OverviewInformation).HasParseError()) {
                    errorInfoapi.eErrLever = Warm;
                    errorInfo.ErrCode = 16131;
                    goto Errorhandling;
                };
                if (json_reqfinal.IsObject() && json_reqfinal.HasMember("PartID ")) {
                    if (!json_reqfinal["PartID "].IsNull()) {
                        partId = json_reqfinal["PartID "].GetString();
                    }
                }
                std::string csvFileName = final_path + "/" + partId + ".csv";

                std::ofstream csvFile(csvFileName.c_str());
                if (!csvFile.is_open()) {
                    std::cerr << "Failed to open CSV file for writing." << std::endl;
                    errorInfo.ErrCode = 16101;
                    //sqlite3_close(db);
                    goto Errorhandling;
                }
                const char* OverviewInformationvertmp = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 5));
                if (OverviewInformationvertmp != NULL) {
                    OverviewInformationver = reinterpret_cast<const char*>(OverviewInformationvertmp);
                }
                else {
                    OverviewInformationver = " ";
                }
                const char* refconftmp = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 6));
                if (refconftmp != NULL) {
                    refconf = reinterpret_cast<const char*>(refconftmp);
                }
                else {
                    refconf = " ";
                }
                const char* refresulttmp = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 7));
                if (refresulttmp != NULL) {
                    refresult = reinterpret_cast<const char*>(refresulttmp);
                }
                else {
                    refresult = " ";
                }
                // Parse JSON data for Overview Information
                rapidjson::Document overviewJson;
                overviewJson.Parse(OverviewInformation);
                if (overviewJson.IsObject() && json_reqfinal.HasMember("FileType")) {
                    csvFile << "Overview Information\n";
                    csvFile << "FileType:," << getStringOrDefault(overviewJson, "FileType", "N/A") << "\n";
                    csvFile << "DeviceName:," << getStringOrDefault(overviewJson, "DeviceName", "N/A") << "\n";
                    csvFile << "sw_version:," << getStringOrDefault(overviewJson, "sw_version", "N/A") << "\n";
                    csvFile << "hd_version:," << getStringOrDefault(overviewJson, "hd_version", "N/A") << "\n";
                    csvFile << "config_file_version:," << getStringOrDefault(overviewJson, "config_file_version", "N/A")  << "\n";
                    csvFile << "PartID:," << getStringOrDefault(overviewJson, "PartID ", "N/A")  << "\n";
                    csvFile << "NcID:," << getStringOrDefault(overviewJson, "NcID", "N/A")  << "\n";
                    csvFile << "NcIP:," << getStringOrDefault(overviewJson, "NcIP ", "N/A")  << "\n";
                    csvFile << "NcModel:," << getStringOrDefault(overviewJson, "NcModel ", "N/A") << "\n";
                    csvFile << "TimeStamp:," << getStringOrDefault(overviewJson, "TimeStamp", "N/A") << "\n";
                }
                // Insert seven blank lines
                for (int i = 0; i < 8; ++i) {
                    csvFile << "\"\"" << "\n";
                }

                // Parse and write Curve Result to CSV file
                if (eoResultjson.IsNull() || !eoResultjson.IsObject() || eoResultjson.MemberCount() == 0) {
                    csvFile << "Curve Result\n"; // 使用默认值
                }
                else {
                    csvFile << "Curve Result\n";
                    csvFile << "Curve TimeMin_T:,0\n";
                    csvFile << "Curve TimeMin_X:,0\n";
                    csvFile << "Curve TimeMin_Force:,0\n";
                    csvFile << "Curve TimeMax_T:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Tmax_T", 0.0) << "\n";
                    csvFile << "Curve TimeMax_X:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Tmax_X", 0.0) << "\n";
                    csvFile << "Curve TimeMax_Force:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Tmax_Y", 0.0) << "\n";
                    csvFile << "Curve TimeAverage:,0\n";
                    csvFile << "Curve Xmin_T:,0\n";
                    csvFile << "Curve Xmin_X:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Xmin_X", 0.0) << "\n";
                    csvFile << "Curve Xmin_Force:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Xmin_Y", 0.0) << "\n";
                    csvFile << "Curve Xmax_T:,0\n";
                    csvFile << "Curve Xmax_X:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Xmax_X", 0.0) << "\n";
                    csvFile << "Curve Xmax_Force:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Xmax_Y", 0.0) << "\n";
                    csvFile << "Curve XAverage:,0\n";
                    csvFile << "Curve ForceMin_T:,0\n";
                    csvFile << "Curve ForceMin_X:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Ymin_X", 0.0) << "\n";
                    csvFile << "Curve ForceMin_Force:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Ymin_Y", 0.0) << "\n";
                    csvFile << "Curve ForceMax_T:,0\n";
                    csvFile << "Curve ForceMax_X:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Ymax_X", 0.0) << "\n";
                    csvFile << "Curve ForceMax_Force:," << getDoubleOrDefault(eoResultjson["CurveResult"], "Ymax_Y", 0.0) << "\n";
                    csvFile << "Curve ForceAverage:,0\n";
                }
                
                for (int i = 0; i < 8; ++i) {
                    csvFile << "\"\"" << "\n";
                }

                rapidjson::Document RefConf;
                if (RefConf.Parse(refconf).HasParseError()) {
                    errorInfoapi.eErrLever = Warm;
                    errorInfo.ErrCode = 16131;
                    csvFile.close();
                    goto Errorhandling;
                };
                // 写入CSV头
                if (RefConf.IsObject() && RefConf.HasMember("RefConf")) {
                    csvFile << "RefConf\n";
                    // 获取RefConf对象
                    const Value& refConf = RefConf["RefConf"];
                    int refCount = getIntOrDefault(refConf, "RefCount", 0);
                    csvFile << "RefCount:," << refCount << "\n";
                    // 遍历Ref1和Ref2
                    for (int i = 1; i <= refCount; ++i) {
                        std::string refKey = "Ref" + std::to_string(i);
                        const Value& ref = refConf[refKey.c_str()];

                        csvFile << refKey << " " << ",CustomRefID:," << getIntOrDefault(ref, "CustomRefID",0);
                        csvFile << ",RefID:," << getIntOrDefault(ref, "RefID", 0);
                        csvFile << ",Global Hy:," << getIntOrDefault(ref, "GlobalHy", 0);
                        csvFile << ",RefxHysteresis:," << getIntOrDefault(ref, "RefxHysteresis", 0);
                        csvFile << ",RefyHysteresis:," << getIntOrDefault(ref, "RefyHysteresis", 0);
                        csvFile << ",EntryStart:," << getIntOrDefault(ref, "EntryStart", 0);
                        csvFile << ",EntryStop:," << getIntOrDefault(ref, "EntryStop", 0);
                        csvFile << ",ExitStart:," << getIntOrDefault(ref, "ExitStart", 0);
                        csvFile << ",ExitStop:," << getIntOrDefault(ref, "ExitStop", 0);
                        csvFile << ",Contains:," << getIntOrDefault(ref, "Contains", 0);
                        csvFile << ",Count:," << getIntOrDefault(ref, "Count", 0);

                        for (int j = 0; j < getIntOrDefault(ref, "Count", 0); ++j) {
                            std::string pointXKey = "Point" + std::to_string(j) + "X";
                            std::string pointYKey = "Point" + std::to_string(j) + "Y";
                            csvFile << "," << pointXKey << ":," << getFloatOrDefault(ref, pointXKey.c_str(), 0.0f);
                            csvFile << "," << pointYKey << ":," << getFloatOrDefault(ref, pointYKey.c_str(), 0.0f);
                        }

                        csvFile << ",FunctionType:," << getIntOrDefault(ref, "FunctionType", 0);
                        csvFile << ",FunctionParaCount:," << getIntOrDefault(ref, "FunctionParaCount", 0);

                        for (SizeType k = 0; k < getIntOrDefault(ref, "FunctionParaCount", 0); ++k) {
                            csvFile << " FunctionPara" << k << ",";
                            if(ref["FunctionPara"][k].IsInt()){ 
                                csvFile << ref["FunctionPara"][k].GetInt(); 
                            }
                            if (k != ref["FunctionPara"].Size() - 1) {
                                csvFile << ",";
                            }
                        }
                        csvFile << ",RefPointX:," << getFloatOrDefault(ref, "RefPointX", 0.0f) ;
                        csvFile << ",RefPointY:," << getFloatOrDefault(ref, "RefPointY", 0.0f)  << "\n";
                    }
                }

                for (int i = 0; i < 8; ++i) {
                    csvFile << "\"\"" << "\n";
                }

                rapidjson::Document EoConf;
                if (EoConf.Parse(eocontent.c_str()).HasParseError()) {
                    errorInfoapi.eErrLever = Warm;
                    errorInfo.ErrCode = 16131;
                    csvFile.close();
                    goto Errorhandling;
                };
                if (EoConf.IsObject() && 
                    EoConf.HasMember("Branchs") && 
                    EoConf["Branchs"].IsArray()&& 
                    EoConf["Branchs"].Size() > 0 && 
                    EoConf["Branchs"][0].IsObject() && 
                    EoConf["Branchs"][0].HasMember("Evaluation")) {
                    const Value& branchs = EoConf["Branchs"];
                    // 读取EvaluationObjects的长度
                    int evaluationObjectsLength = 0;
                    if (branchs[0]["Evaluation"].HasMember("EvaluationObjects") && branchs[0]["Evaluation"]["EvaluationObjects"].IsArray()) {
                        evaluationObjectsLength = branchs[0]["Evaluation"]["EvaluationObjects"].Size();
                    }
                    csvFile << "EoConf\n";
                    csvFile << "EoCount:," << evaluationObjectsLength << "\n";
                    for (SizeType j = 0; j < evaluationObjectsLength; ++j) {
                        const Value& evaluation = branchs[0]["Evaluation"];
                        const Value& evalObj = evaluation["EvaluationObjects"][j];
                        csvFile << "Eo" << j << ";";
                        int EoType = getIntOrDefault(evalObj, "EoType",-1);
                        csvFile << ",Type:," << EoType;
                        csvFile << ",Reference Identity:," << "0";
                        if (evalObj.IsObject() && evalObj.HasMember("Property")) {
                            const Value& property = evalObj["Property"];
                            bool useGlobalHysteresis = !property["IsProgramHysteresis"].GetBool();
                            float  xHysteresis, yHysteresis;
                            if (useGlobalHysteresis) {
                                xHysteresis = getFloatOrDefault(evaluation,"XHysteresis",0.0f) ;
                                yHysteresis = getFloatOrDefault(evaluation, "YHysteresis", 0.0f);
                            }
                            else {
                                xHysteresis = getFloatOrDefault(evaluation,"XHysteresis", 0.0f);
                                yHysteresis = getFloatOrDefault(evaluation, "YHysteresis", 0.0f);
                            }
                            csvFile << ",Use Global Hysteresis:," << (useGlobalHysteresis ? "1" : "0");
                            csvFile << ",xHysteresis:," << xHysteresis;
                            csvFile << ",yHysteresis:," << yHysteresis;
                            csvFile << ",Involed evaluation:," << (property["IsInvolved"].GetBool() ? "Involved" : "Not Involved");
                        }
                        else {
                            errorInfo.ErrCode = 16093;
                            goto Errorhandling;
                        }
                        if (evalObj.IsObject() && evaluation["EvaluationObjects"][j].HasMember("EoContent")){
                            const Value& LineorPolygonPoints = evaluation["EvaluationObjects"][j]["EoContent"];
                            if (EoType == 0) {
                                csvFile << ",EntryStart:," << getFloatOrDefault(LineorPolygonPoints, "EntryStart", 0.0f);
                                csvFile << ",EntryStop:," << getFloatOrDefault(LineorPolygonPoints, "EntryStop", 0.0f)  ;
                                csvFile << ",Point0X:," << getFloatOrDefault(LineorPolygonPoints["LinePoints"][0]["X"], "Value", 0.0f);
                                csvFile << ",Point0Y:," << getFloatOrDefault(LineorPolygonPoints["LinePoints"][0]["Y"], "Value", 0.0f);
                                csvFile << ",Point1X:," << getFloatOrDefault(LineorPolygonPoints["LinePoints"][1]["X"], "Value", 0.0f);
                                csvFile << ",Point1Y:," << getFloatOrDefault(LineorPolygonPoints["LinePoints"][1]["Y"], "Value", 0.0f);
                                csvFile << ",FieldbusResultCount:," << "0";

                            }
                            if (EoType == 1) {
                                csvFile << ",EntryStart:," << getFloatOrDefault(LineorPolygonPoints, "EntryStart", 0.0f) ;
                                csvFile << ",EntryStop:," << getFloatOrDefault(LineorPolygonPoints, "EntryStop", 0.0f) ;
                                csvFile << ",ExitStart:," << getFloatOrDefault(LineorPolygonPoints, "ExitStart", 0.0f) ;
                                csvFile << ",ExitStop:," << getFloatOrDefault(LineorPolygonPoints, "ExitStop", 0.0f) ;
                                bool Contains = getBoolOrDefault(LineorPolygonPoints, "IsInside",0);
                                csvFile << ",Contains:," << (Contains ? "1" : "0");
                                csvFile << ",PoinstCount:," << getIntOrDefault(LineorPolygonPoints, "PointCount",0);
                                csvFile << ",Point0X:," << getFloatOrDefault(LineorPolygonPoints["PolygonPoints"][0]["X"], "Value", 0.0f);
                                csvFile << ",Point0Y:," << getFloatOrDefault(LineorPolygonPoints["PolygonPoints"][0]["Y"], "Value", 0.0f);
                                csvFile << ",Point1X:," << getFloatOrDefault(LineorPolygonPoints["PolygonPoints"][1]["X"], "Value", 0.0f);
                                csvFile << ",Point1Y:," << getFloatOrDefault(LineorPolygonPoints["PolygonPoints"][1]["Y"], "Value", 0.0f);
                                csvFile << ",Point2X:," << getFloatOrDefault(LineorPolygonPoints["PolygonPoints"][2]["X"], "Value", 0.0f);
                                csvFile << ",Point2Y:," << getFloatOrDefault(LineorPolygonPoints["PolygonPoints"][2]["Y"], "Value", 0.0f);
                                csvFile << ",Point3X:," << getFloatOrDefault(LineorPolygonPoints["PolygonPoints"][3]["X"], "Value", 0.0f);
                                csvFile << ",Point3Y:," << getFloatOrDefault(LineorPolygonPoints["PolygonPoints"][3]["Y"], "Value", 0.0f);
                                int  TunnelCount = getIntOrDefault(LineorPolygonPoints,"TunnelCount",0);
                                csvFile << ",TunnelCount:," << TunnelCount;
                                for (SizeType t = 0; t < TunnelCount; ++t) {
                                    std::string tunnelStartKey = "Tunnex" + std::to_string(t + 1) + "Start";
                                    std::string tunnelEndKey = "Tunnex" + std::to_string(t + 1) + "End";
                                    csvFile << "," << tunnelStartKey << ":," << getIntOrDefault(LineorPolygonPoints["Tunnels"][t], "Start", 0);
                                    csvFile << "," << tunnelEndKey << ":," << getIntOrDefault(LineorPolygonPoints["Tunnels"][t], "Stop", 0) ;
                                }
                                if (evalObj.IsObject() && evalObj.HasMember("Calculations")) {
                                    const Value& calculations = evalObj["Calculations"];
                                    int CalcCount = calculations.Size();
                                    int visibleCalculationCount = 0;
                                    for (SizeType c = 0; c < CalcCount; ++c) {
                                        if (getBoolOrDefault(calculations[c], "Visiable", 0)) {
                                            visibleCalculationCount++;
                                        }
                                    }
                                    csvFile << ",CalcCount:," << visibleCalculationCount;

                                    const char* calculationTypes[] = { "AverageY", "Time", "Speed", "Inflexion" };
                                    int calcIndex = 0;
                                    for (SizeType c = 0; c < CalcCount; ++c) {
                                        if (getBoolOrDefault(calculations[c], "Visiable", 0)) {
                                            csvFile << ",CalcType:," << calculationTypes[calcIndex];
                                            if (calcIndex == 3) {  // Inflexion case
                                                csvFile << ",MinBending:," << getIntOrDefault(calculations[c],"Min",0);
                                                csvFile << ",MaxBending:," << getIntOrDefault(calculations[c], "Max", 0);
                                                csvFile << ",dx:," << 0;  // Assuming dx is 0 as per your example
                                            }
                                            else {
                                                std::string minField = "Min" + std::string(calculationTypes[calcIndex]);
                                                std::string maxField = "Max" + std::string(calculationTypes[calcIndex]);
                                                csvFile << "," << minField << ":," << getIntOrDefault(calculations[c], "Min", 0);
                                                csvFile << "," << maxField << ":," << getIntOrDefault(calculations[c], "Max", 0);
                                            }
                                            calcIndex++;
                                        }
                                    }
                                }
                                csvFile << ",FieldbusResultCount:," << "0";
                            }
                        }
                        else {
                            errorInfo.ErrCode = 16093;
                            goto Errorhandling;
                        }
                    }
                }
                else {
                    csvFile << "EoConf\n";
                }
                for (int i = 0; i < 8; ++i) {
                    csvFile << "\"\"" << "\n";
                }

                rapidjson::Document RefResult;
                if (RefResult.Parse(refresult).HasParseError()) {
                    errorInfoapi.eErrLever = Warm;
                    errorInfo.ErrCode = 16131;
                    csvFile.close();
                    goto Errorhandling;
                };
                if (RefResult.IsObject() && RefResult.HasMember("RefResult")) {
                    const Value& refResult = RefResult["RefResult"];
                    csvFile << "RefResult\n";
                    csvFile << "RefCount:," << getIntOrDefault(refResult,"RefCount",0) << "\n";

                    for (SizeType i = 1; i <= getIntOrDefault(refResult, "RefCount", 0); ++i) {
                        std::string refKey = "Ref" + std::to_string(i);
                        const Value& ref = refResult[refKey.c_str()];

                        csvFile << refKey << ":";
                        csvFile << ",RefId:," << getIntOrDefault(ref, "RefId", 0);
                        csvFile << ",bFindValue:," << getIntOrDefault(ref, "bFindValue", 0);
                        csvFile << ",PointX:," << getDoubleOrDefault(ref, "PointX", 0.0);
                        csvFile << ",PointY:," << getDoubleOrDefault(ref, "PointY", 0.0);
                        csvFile << ",";
                        csvFile << "\n";
                    }
                }
                for (int i = 0; i < 8; ++i) {
                    csvFile << "\"\"" << "\n";
                }
                csvFile << "EoResult\n";
                csvFile << "OK/NG,0\n";
                if (eoResultjson.IsObject() && eoResultjson.HasMember("EoResult") && eoResultjson["EoResult"].IsArray()) {
                    csvFile << "EoCount:," << eoResultjson["EoResult"].Size();
                    const Value& eoResults = eoResultjson["EoResult"];
                    for (SizeType i = 0; i < eoResults.Size(); ++i) {
                        const Value& eo = eoResults[i];
                        csvFile << "\nEo" << getIntOrDefault(eo, "EoIndex",0)<< ":,Type:," << getIntOrDefault(eo, "EoType", 0) << ",Result:," << getBoolOrDefault(eo, "EoPass", 0);
                        csvFile << ",Error Information:," << getStringOrDefault(eo, "ErrorCode","N/A");

                        bool foundCalc = false;
                        int calcCount = 0;
                        if (eo.IsObject() && getIntOrDefault(eo, "EoType", 0)== 0 && eo.HasMember("ResultItem") && eo["ResultItem"].IsArray()) {
                            for (const auto& item : eo["ResultItem"].GetArray()) {
                                if (item.IsObject() && item.HasMember("Active") && item["Active"].IsBool()) {
                                    if (item["Active"].GetBool()) {
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".VarIsOK:," << (getBoolOrDefault(item, "IsPass", 0) ? "1" : "0");
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".RealVar:," << getDoubleOrDefault(item, "RealValue", 0.0);
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".MinSet:," << getDoubleOrDefault(item, "MinRange", 0.0);
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".MaxSet:," << getDoubleOrDefault(item, "MaxRange", 0.0);
                                    }
                                    else {
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".VarIsOK:," << 0;
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".RealVar:," << 0;
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".MinSet:," << 0;
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".MaxSet:," << 0;
                                    }
                                }
                            }
                        }
                        if (eo.IsObject() && getIntOrDefault(eo, "EoType", 0) == 1 && eo.HasMember("ResultItem") && eo["ResultItem"].IsArray()) {
                            for (const auto& item : eo["ResultItem"].GetArray()) {
                                if (item.IsObject() && item.HasMember("Active") && item["Active"].IsBool()) {
                                    if (item["Active"].GetBool()) {
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".VarIsOK:," << (getBoolOrDefault(item, "IsPass", 0) ? "1" : "0");
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".RealVar:," << getDoubleOrDefault(item, "RealValue", 0.0);
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".MinSet:," << getDoubleOrDefault(item, "MinRange", 0.0);
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".MaxSet:," << getDoubleOrDefault(item, "MaxRange", 0.0);
                                    }
                                    else {
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".VarIsOK:," << 0;
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".RealVar:," << 0;
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".MinSet:," << 0;
                                        csvFile << "," << getStringOrDefault(item, "Name", "N/A") << ".MaxSet:," << 0;
                                    }
                                    std::string itemName = getStringOrDefault(item, "Name", "N/A");
                                    if ((itemName == "AverageY" || itemName == "MinTime" || itemName == "MinSpeed" || itemName == "MinInflexion") && item["Active"].GetBool()) {
                                        foundCalc = true;
                                        calcCount++;
                                    }
                                }
                            }
                            csvFile << ",CalcCount:," << calcCount << ",";

                            for (const auto& item : eo["ResultItem"].GetArray()) {
                                std::string itemName = getStringOrDefault(item, "Name", "N/A");
                                if (item.IsObject() && item.HasMember("Active") && item["Active"].IsBool()) {

                                    if (foundCalc && item["Active"].GetBool() && itemName == "AverageY") {
                                        csvFile << ",CalcType:," << getStringOrDefault(item, "Name", "N/A");
                                        csvFile << ",ResultItem:, VarIsOK:," << (getBoolOrDefault(item, "IsPass", 0)? "1" : "0");
                                        csvFile << ",RealVar:," <<  getDoubleOrDefault(item, "RealValue", 0.0);
                                        csvFile << ",MinSet:," << getDoubleOrDefault(item, "MinRange", 0.0);
                                        csvFile << ",MaxSet:," << getDoubleOrDefault(item, "MaxRange", 0.0);
                                    }
                                    if (foundCalc && item["Active"].GetBool() && (itemName == "MinTime" || itemName == "MinSpeed" || itemName == "MinInflexion")) {
                                        std::string calcType = itemName.substr(3);  // 去掉前三个字母
                                        csvFile << ",CalcType:," << calcType;
                                        csvFile << ",MinResultItem:, VarIsOK:," << (getBoolOrDefault(item, "IsPass", 0) ? "1" : "0");
                                        csvFile << ",RealVar:," << getDoubleOrDefault(item, "RealValue", 0.0);
                                        csvFile << ",MinSet:," << getDoubleOrDefault(item, "MinRange", 0.0);
                                        csvFile << ",MaxSet:," << getDoubleOrDefault(item, "MaxRange", 0.0);
                                    }
                                    if (foundCalc && item["Active"].GetBool() && (itemName == "MaxTime" || itemName == "MaxSpeed" || itemName == "MaxInflexion")) {
                                        csvFile << ",MaxResultItem:, VarIsOK:," << (getBoolOrDefault(item, "IsPass", 0) ? "1" : "0");
                                        csvFile << ",RealVar:," << getDoubleOrDefault(item, "RealValue", 0.0);
                                        csvFile << ",MinSet:," << getDoubleOrDefault(item, "MinRange", 0.0);
                                        csvFile << ",MaxSet:," << getDoubleOrDefault(item, "MaxRange", 0.0);
                                    }
                                    if (foundCalc && item["Active"].GetBool() && (itemName == "InflexCoordX" || itemName == "InflexCoordY")) {
                                        csvFile << ",VarIsOK:," << (getBoolOrDefault(item, "IsPass", 0) ? "1" : "0");
                                        csvFile << ",RealVar:," << getDoubleOrDefault(item, "RealValue", 0.0);
                                        csvFile << ",MinSet:," << getDoubleOrDefault(item, "MinRange", 0.0);
                                        csvFile << ",MaxSet:," << getDoubleOrDefault(item, "MaxRange", 0.0);
                                    }
                                }
                            }
                        }
                    }
                    csvFile << "\n";
                }
                

                for (int i = 0; i < 8; ++i) {
                    csvFile << "\"\"" << "\n";
                }
                const char* channelLabel[] = { "x", "y", "z" };
                rapidjson::Document Channelcontentjson;
                if (Channelcontentjson.Parse(Channelcontent.c_str()).HasParseError()) {
                    errorInfoapi.eErrLever = Warm;
                    errorInfo.ErrCode = 16131;
                    csvFile.close();
                    goto Errorhandling;
                };

                // 绑定 ChartInfo 的 MD5 值并执行查询
                sqlite3_bind_text(stmtselectChannel, 1, ChartInfo.c_str(), -1, SQLITE_STATIC);
                if (sqlite3_step(stmtselectChannel) == SQLITE_ROW) {
                    const unsigned char* ChartInfocontenttmp = sqlite3_column_text(stmtselectChannel, 0);
                    if (ChartInfocontenttmp != NULL) {
                        ChartInfocontent = reinterpret_cast<const char*>(ChartInfocontenttmp);
                    }
                    else {
                        ChartInfocontent = " ";
                    }
                }
                else {
                    errorInfo.ErrCode = 16096;
                    goto Errorhandling;
                }
                rapidjson::Document ChartInfocontentjson;
                if (ChartInfocontentjson.Parse(ChartInfocontent).HasParseError()) {
                    errorInfoapi.eErrLever = Warm;
                    errorInfo.ErrCode = 16131;
                    csvFile.close();
                    goto Errorhandling;
                };

                std::string mode = getStringOrDefault(ChartInfocontentjson["Sample"],"Mode", "N/A");
                int modeValue = modeMap[mode];
                csvFile << "Curve Info\n";
                csvFile << "SampleIndex:," << modeValue << "\n";
                if (!ChartInfocontentjson["Sample"].HasMember("Rate")) {
                    csvFile << "SampleRate:, 1\n";
                }
                else {
                    std::string rate = getStringOrDefault(ChartInfocontentjson["Sample"], "Rate", "N/A");
                    int rateValue = rateMap[mode][rate];
                    csvFile << "SampleRate:," << rateValue << "\n";
                }
                csvFile << "BeginTime:, 4.61284E+18\n";

                // Write dynamic channel info
                if (Channelcontentjson.IsArray()) {
                    csvFile << "ChannelCount:, " << Channelcontentjson.Size() << "\n";

                    for (SizeType i = 0; i < Channelcontentjson.Size(); ++i) {
                        const Value& channel = Channelcontentjson[i];
                        if (channel["Active"].GetBool()) {
                            csvFile << "ChannelType_" << channelLabel[i] << ": ," << getStringOrDefault(channel,"ChannelName","N/A")<< "\n";
                            csvFile << "DisplayFormat:,x.";
                            for (int j = 0; j < getIntOrDefault(channel,"Precision",0); ++j) {
                                csvFile << "x";
                            }
                            csvFile << "\n";
                            csvFile << "DataUnit:," << getStringOrDefault(channel, "Unit", "N/A")<< "\n";
                        }
                    }
                }

                for (int i = 0; i < 8; ++i) {
                    csvFile << "\"\"" << "\n";
                }

                const void* chartdata = sqlite3_column_blob(stmt, 2);
                int chartsize = sqlite3_column_bytes(stmt, 2);
                int sampleCount = chartsize / 12;
                csvFile << "SampleCount:," << sampleCount << "\n";
                csvFile << "Time: s,Position: mm,Force: kN\n";
                const char* data = static_cast<const char*>(chartdata);
                for (int i = 0; i < sampleCount; ++i) {
                    float time = BinaryToFloat(data + i * 12);
                    float position = BinaryToFloat(data + i * 12 + 4);
                    float force = BinaryToFloat(data + i * 12 + 8);

                    csvFile << time << "," << position << "," << force << "\n";
                }
                csvFile.close();

                //char update_sql[256];
                //snprintf(update_sql, sizeof(update_sql), "UPDATE Result SET Csvflag = 1 WHERE rowid = %d;", rowid);
                //int update_ret = sqlite3_exec(db, update_sql, NULL, NULL, NULL);
                //if (update_ret != SQLITE_OK) {
                //    std::cerr << "Failed to update record: " << sqlite3_errmsg(db) << std::endl;
                //    errorInfo.ErrCode = 16098;
                //    goto Errorhandling;
                //    // 处理错误
                //}
                sqlite3_reset(stmt);
                //sqlite3_reset(stmtselectpf);
                //sqlite3_reset(stmtselecteoct);
                sqlite3_reset(stmtselectChannel);

            }
            else if (rc == SQLITE_DONE){
                break;
            }
            else {
                errorInfo.ErrCode = 16119;
                goto Errorhandling;
            }
        }
        else {
            errorInfo.ErrCode = 16120;
            goto Errorhandling;
        }
    }
    Errorhandling:
    sqlite3_finalize(stmt);
    sqlite3_finalize(stmtselectpf);
    sqlite3_finalize(stmtselecteoct);
    sqlite3_finalize(stmtselectChannel);
    ErrorInfoPack(&errorInfo, (char*)"dataServer", "");
    return  errorInfo;
}

bool is_mounted(const std::string& path) {
    std::string cmd = "mount | grep " +path + " > /dev/null 2>&1";;
    return (system(cmd.c_str()) == 0);  // 返回 0 表示路径已挂载
}

bool unmount_path(const std::string& path) {
    std::string cmd = "sudo umount " + path;
    return (system(cmd.c_str()) == 0);  // 返回 0 表示卸载成功
}

bool is_path_busy(const std::string& path) {
    std::string cmd = "lsof " + path + " > /dev/null 2>&1";
    int status = system(cmd.c_str());
    return (WEXITSTATUS(status) == 0);
}

bool compulsory_unmount_path(const std::string& path) {
    sleep(5);
    if (!is_path_busy(path)) {
        std::string cmd = "sudo umount -l " + path;
        return (system(cmd.c_str()) == 0);
    }
    else {
        std::cerr << "挂载点繁忙，无法卸载" << std::endl;
        return 0;
    }
}
    

bool create_directory(const std::string& path) {
    return (mkdir(path.c_str(), 0777) == 0);  // 创建目录并设置权限为 755
}

/*dataServer		                    数据服务器函数
* @return        errorInfo.ErrCode非0   = 数据服务器开启失败
* @return        errorInfo.ErrCode = 0  = 数据服务器开启成功
*/
ErrorInfo FileServer(bool* pbConnectTest, bool* pbConnectState, bool* pbActiveState)
{
    *pbConnectState = false;
    *pbActiveState = false;
    int rc;
    rapidjson::Document json_req;
    ErrorInfo errorInfo;
    memset(&errorInfoapi, 0, sizeof(ErrorInfo));
    bserverConnectflag = false;
    //sqlite3* db;
    //sqlite3* dbresult;
    sqlite3_stmt* stmt = nullptr;
    struct stat st;
    bool alreadyexecutflag = false;
    bool Enable;
    bool continueflag;
    std::string ip ;
    std::string User  ;
    std::string Password ;
    std::string Directory ;
    std::string PathRule ;
    std::string DeviceConfigResult;
    const unsigned char* DeviceConfigResultTmp;
    sqlite3_stmt* seletemaxstmt = nullptr;
    std::string querymaxid = "SELECT MAX(Id) FROM Result;";
    std::string sqlDeviceConfig = "SELECT Content FROM DeviceConfig WHERE Item = 'DataService'; ";
    std::string path;
    //int rc = sqlite3_open_v2("/userdata/resultDb.lx", &dbresult, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    //rc += sqlite3_open_v2("/root/press.lx", &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    //rc += sqlite3_wal_checkpoint_v2(db, NULL, SQLITE_CHECKPOINT_TRUNCATE, 0, 0);
    //rc += sqlite3_exec(db, "PRAGMA wal_checkpoint(TRUNCATE)", nullptr, nullptr, 0);
    //rc += sqlite3_exec(db, "PRAGMA journal_mode=WAL;", NULL, NULL, NULL);
    //rc += sqlite3_exec(db, "PRAGMA FOREIGN_KEYS=ON;", NULL, NULL, NULL);
    //rc += sqlite3_exec(db, "PRAGMA SYNCHRONOUS=NORMAL", NULL, NULL, NULL);
    /*while (rc == SQLITE_OK) {
        int count = 0;
        count++;
        count--;
        sleep(1);
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
    }*/
   /* if (rc != SQLITE_OK) {
        std::cerr << "Failed to open SQLite database: " << sqlite3_errmsg(db) << std::endl;
        sqlite3_close_v2(db);
        sqlite3_close_v2(dbresult);
        errorInfo.ErrCode = 16080;
        goto Errorhandling;
    }*/
    std::this_thread::sleep_for(std::chrono::seconds(1)); // 暂停 1 秒
    while (true) {
        rc = sqlite3_prepare_v3(db, sqlDeviceConfig.c_str(), sqlDeviceConfig.length(), 0, &stmt, NULL);
        if (rc != SQLITE_OK) {
            printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            //sqlite3_close_v2(db_result);
            errorInfoapi.eErrLever = Error;
            errorInfoapi.ErrCode = 16086;
            ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
            sleep(10);
            //goto Errorhandling;
        }
        else {
            rc = sqlite3_step(stmt);
            if (rc== SQLITE_ROW) {
                continueflag = true;
                break;
            }
            else
            {
                sqlite3_finalize(stmt);
                //sqlite3_close_v2(db);
                //sqlite3_close_v2(db_result);
                errorInfoapi.eErrLever = Error;
                errorInfoapi.ErrCode = 16090;
                ErrorInfoPack(&errorInfoapi, "FileServer", "");
                sleep(10);
            }
            
        }
    }
    if(continueflag)
    {
        while (1) 
        {
            *pbActiveState = false;
            if (alreadyexecutflag) {
                rc = sqlite3_step(stmt);
            }
            if (rc == SQLITE_ROW) {
                alreadyexecutflag = true;
                DeviceConfigResultTmp = sqlite3_column_text(stmt, 0);
                if (DeviceConfigResultTmp != NULL) {
                    DeviceConfigResult = reinterpret_cast<const char*>(DeviceConfigResultTmp);
                    if (json_req.Parse(DeviceConfigResult.c_str()).HasParseError()) {
                        std::cerr << "设备配置文件格式错误" << std::endl;
                        sqlite3_reset(stmt);
                        errorInfoapi.eErrLever = Error;
                        errorInfoapi.ErrCode = 16132;
                        ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
                    }

                    if (strcmp(DeviceConfigResult.c_str(), "0") != 0 && strlen((char*)DeviceConfigResult.c_str()) > 0)
                    {
                        Enable = json_req["NC"]["Enabled"].GetBool();
                        ip = json_req["Service"]["IP"].GetString();
                        User = json_req["Service"]["User"].GetString();
                        Password = json_req["Service"]["Password"].GetString();
                        Directory = json_req["Service"]["Directory"].GetString();
                        PathRule = json_req["Service"]["PathRule"].GetString();
                    }

                    path = "/userdata/dir_test";
                    if (stat(path.c_str(), &st) == -1) {
                        std::cerr << "/userdata/dir_test路径不存在"  << std::endl;
                        // 检查是否已挂载
                        if (is_mounted(path)) {
                            std::cout << path << " 已挂载，正在卸载..." << std::endl;
                            // 尝试卸载
                            if (unmount_path(path)) {
                                std::cout << path << " 卸载成功" << std::endl;

                                // 卸载成功后创建新目录
                                if (mkdir(path.c_str(), 0777) == -1){
                                    std::cout << path << " 目录创建成功" << std::endl;
                                }
                                else {
                                    std::cerr << path << " 目录创建失败，请检查权限" << std::endl;
                                    sqlite3_reset(stmt);
                                    errorInfoapi.eErrLever = Warm;
                                    errorInfoapi.ErrCode = 16087;
                                    ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
                                }

                            }
                            else {
                                std::cerr << path << " 卸载失败，请检查权限或挂载状态" << std::endl;
                                sqlite3_reset(stmt);
                                errorInfoapi.eErrLever = Warm;
                                errorInfoapi.ErrCode = 16128;
                                ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
                            }
                        }
                        else {
                            // 如果未挂载直接创建目录
                            if (create_directory(path)) {
                                std::cout << path << " 目录创建成功" << std::endl;
                            }
                            else {
                                std::cerr << path << " 目录创建失败，请检查权限" << std::endl;
                                sqlite3_reset(stmt);
                                errorInfoapi.eErrLever = Warm;
                                errorInfoapi.ErrCode = 16129;
                                ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
                            }
                        }
                    }
                    sqlite3_reset(stmt);
                    if (Enable || *pbConnectTest) {
                        bool physicsconnectflag = false;
                        std::ifstream carrierFile("/sys/class/net/eth2/carrier");
                        std::string carrierStatus;
                        if (carrierFile.is_open()) {
                            std::getline(carrierFile, carrierStatus);
                            carrierFile.close();
                            if (carrierStatus == "1") {
                                physicsconnectflag = true;
                            }
                            else {
                                if (Enable == false && *pbConnectTest == true) {
                                    *pbConnectTest = false;
                                }
                                *pbConnectState = false;
                                errorInfoapi.eErrLever = Warm;
                                errorInfoapi.ErrCode = 16134;
                                ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
                            }
                        }
                        if (physicsconnectflag){
                            errorInfo = mountCIFSShare(ip, Directory, User, Password, pbConnectTest);
                            if (errorInfo.ErrCode == 0) {
                                std::cout << "挂载成功" << std::endl;
                                const char* PathRulelast = PathRule.c_str();
                                rc = sqlite3_prepare_v3(db_result, querymaxid.c_str(), querymaxid.length(), 0, &seletemaxstmt, NULL);
                                if (rc != SQLITE_OK) {
                                    sqlite3_finalize(seletemaxstmt);
                                    errorInfoapi.eErrLever = Error;
                                    errorInfoapi.ErrCode = 16117;
                                    ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
                                    //goto Errorhandling;
                                }
                                //执行查询
                                if (sqlite3_step(seletemaxstmt) == SQLITE_ROW) {
                                    MaxId = sqlite3_column_int(seletemaxstmt, 0);
                                    if (MaxId == 0) {
                                        MaxId = 1;
                                    }
                                    std::cout << "最大的Id是: " << MaxId << std::endl;
                                    sqlite3_finalize(seletemaxstmt);
                                }
                                else {
                                    std::cout << "表中没有记录:" << sqlite3_errmsg(db_result) << std::endl;
                                    sqlite3_finalize(seletemaxstmt);
                                    errorInfoapi.eErrLever = Error;
                                    errorInfoapi.ErrCode = 16118;
                                    ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
                                    //goto Errorhandling;
                                }
                                //int dataServiceCount = _ApiStateCounter.dataService;
                                while (Enable || *pbConnectTest) {
                                    if (is_mounted(path) && is_interface_up("eth2")) {
                                        errorInfo = resultDirDeal(db, db_result, const_cast<char*>(PathRulelast));
                                        if (errorInfo.ErrCode != 0) {
                                            std::cerr << "csv转换过程中格式错误" << errorInfo.ErrCode << std::endl;
                                            
                                            if (errorInfo.ErrCode == 16101) {
                                                std::cerr << "网络不可用，跳出循环等待恢复..." << std::endl;

                                                // 退出当前循环
                                                *pbActiveState = false;
                                                *pbConnectState = false;

                                                // 跳出循环
                                                break;
                                            }
                                            //sqlite3_finalize(stmt);
                                            //sqlite3_close_v2(db);
                                            //sqlite3_close_v2(db_result);
                                            *pbActiveState = false;
                                            *pbConnectState = false;
                                            errorInfoapi.eErrLever = Warm;
                                            errorInfoapi.ErrCode = 16089;
                                            ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
                                            //goto Errorhandling;
                                        }
                                        else {
                                            *pbActiveState = true;
                                            if (FileServerControl) {
                                                if (unmount_path(path)) {
                                                    std::cout << path << " 卸载成功" << std::endl;
                                                    FileServerControl = false;
                                                    *pbActiveState = false;
                                                    *pbConnectState = false;
                                                    break;
                                                }
                                                else {
                                                    std::cerr << "卸载测试失败" << std::endl;
                                                    *pbActiveState = false;
                                                    *pbConnectState = false;
                                                    sleep(1);
                                                    break;
                                                }
                                            }
                                            if (*pbConnectTest == true) {
                                                std::cerr << "开始测试" << std::endl;
                                                *pbConnectTest = false;
                                                *pbConnectState = true;
                                                break;
                                            }
                                        }
                                    }
                                    else {
                                        if (Enable == false && *pbConnectTest == true) {
                                            *pbConnectTest = false;
                                        }
                                        if (!is_interface_up("eth2")) {
                                            if (compulsory_unmount_path(path)) {
                                                std::cout << path << " 卸载成功" << std::endl;
                                            }
                                            *pbActiveState = false;
                                            *pbConnectState = false;
                                            errorInfoapi.eErrLever = Warm;
                                            errorInfoapi.ErrCode = 16088;
                                        }
                                        break;
                                    }
                                    sleep(1);
                                }
                            }
                            else {
                                std::cout << "挂载失败" << errorInfo.ErrCode << std::endl;
                                errorInfoapi.eErrLever = Warm;
                                errorInfoapi.ErrCode = 16088;
                                ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
                                //goto Errorhandling;
                            }
                        }
                    }
                    sleep(5);
                }
                else {
                    DeviceConfigResult = "";
                    sqlite3_reset(stmt);
                    sleep(3);
                }
            }
            else {
                sqlite3_reset(stmt);
                sleep(10);
                //sqlite3_close_v2(db);
                //sqlite3_close_v2(db_result);
                errorInfoapi.eErrLever = Error;
                errorInfoapi.ErrCode = 16090;
                ErrorInfoPack(&errorInfoapi, (char*)"FileServer", "");
                //goto Errorhandling;
            }
        }
        sleep(5);
    }
    //Errorhandling:
    //ErrorInfoPack(&errorInfo, "dataServer", "");
    return  errorInfo;
}

/*Writebacksqlite		                回写数据库文件的函数/test
* @return        errorInfo.ErrCode非0   = 处理结果失败
* @return        errorInfo.ErrCode = 0  = 处理结果成功
*/
ErrorInfo Writebacksqlite()
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    const char* dbPath = "/root/press.lx";
    const int interval = 2; // 检查间隔时间(秒)
    const int maxSize = 1; // 100MB
    //sqlite3* db; 
    char walFilePath[256];
    snprintf(walFilePath, sizeof(walFilePath), "%s-wal", dbPath);
    //int rc = sqlite3_open_v2("/root/press.lx", &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, nullptr);
    while (true) {
        struct stat st;
        if (stat(walFilePath, &st) == 0) {
            // WAL文件存在,检查大小
            off_t walFileSize = st.st_size;
            if (walFileSize > maxSize) {
                char* errMsg = nullptr;
                int rc = sqlite3_exec(db, "PRAGMA wal_checkpoint(TRUNCATE)", nullptr, nullptr, &errMsg);
                if (rc != SQLITE_OK) {
                    fprintf(stderr, "Failed to execute wal_checkpoint: %s\n", errMsg);
                    //sqlite3_close_v2(db);
                    sqlite3_free(errMsg);
                    errorInfo.ErrCode = 16086;
                    goto Errorhandling;
                }
            }
        }
        sleep(interval); // 等待指定时间后再次检查
    }
    Errorhandling:
    //sqlite3_close_v2(db);
    ErrorInfoPack(&errorInfo, (char*)"Writebacksqlite", "");
    return  errorInfo;
}

/* WriteStatisticsOp		        将统计数据写入数据库
* @param[in]     int Fid 		        = 插入的工艺号(0标识全局统计数据   1-255表示工艺号)
* @param[in]     char* content          = 插入的统计内容
* @param[in]     char* version		    = 统计数据版本号
* @return        errorInfo.ErrCode非0   = 写入结果失败
* @return        errorInfo.ErrCode = 0  = 写入结果成功
*/
ErrorInfo WriteStatisticsOp(int Fid, char* content, char* version) {
    char* query_sql;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
   /* sqlite3* db;
    int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.ErrCode = 16087;
        goto Errorhandling;
    }*/
    sqlite3_stmt* stmt;
    query_sql = (char*)"INSERT OR REPLACE INTO StatisticsData (Fid ,Ver,Content) VALUES (?, ?, ?);";
    rc = sqlite3_prepare_v2(db, query_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        printf("sql语句语法错误: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.ErrCode = 16074;
        goto Errorhandling;
    }
    rc = sqlite3_bind_int(stmt, 1, Fid);
    rc += sqlite3_bind_text(stmt, 2, version, -1, SQLITE_STATIC);
    rc += sqlite3_bind_text(stmt, 3, content, -1, SQLITE_STATIC);
    if (rc == SQLITE_OK) {
        rc = sqlite3_step(stmt);
    }
    else {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.ErrCode = 16075;
        goto Errorhandling;
    }
    if (rc != SQLITE_DONE) {
        printf("插入记录失败: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.ErrCode = 16076;
        goto Errorhandling;
    }
    sqlite3_finalize(stmt);
    //sqlite3_close_v2(db);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"WriteStatisticsOp", "");
    return  errorInfo;
}

/* ReadStatisticsOp		            读取统计数据数据库
* @param[in]     int Fid 		        = 读取的工艺号(0标识全局统计数据   1-255表示工艺号)
* @param[in]     char* version		    = 统计数据版本号
* @return        errorInfo.ErrCode非0   = 写入结果失败
* @return        errorInfo.ErrCode = 0  = 写入结果成功
*/
ErrorInfo ReadStatisticsOp(int Fid, char* version, std::string* content) {
    char* select_sql;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    /*sqlite3* db;
    int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
    if (rc != SQLITE_OK) {
        printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
        errorInfo.ErrCode = 16091;
        goto Errorhandling;
    }*/
    sqlite3_stmt* stmt;
    select_sql = (char*)"SELECT Content FROM StatisticsData WHERE Fid=? AND Ver=?";
    rc = sqlite3_prepare_v2(db, select_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.ErrCode = 16078;
        *content = "Error_1";
        goto Errorhandling;
    }
    rc = sqlite3_bind_int(stmt, 1, Fid);
    rc += sqlite3_bind_text(stmt, 2, version, -1, SQLITE_STATIC);
    if (rc == SQLITE_OK) {
        rc = sqlite3_step(stmt);
    }
    else {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.ErrCode = 16079;
        *content = "Error_2";
        goto Errorhandling;
    }
    if (rc == SQLITE_ROW) {
        const unsigned char* pTest = sqlite3_column_text(stmt, 0);
        if (pTest != NULL)
        {
            *content = reinterpret_cast<const char*>(pTest);
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            goto Errorhandling;
        }
        else
        {
            sqlite3_finalize(stmt);
            //sqlite3_close_v2(db);
            *content =  "Error_3";
            goto Errorhandling;
        }
    }
    else 
    {
        sqlite3_finalize(stmt);
        *content = "Error_NULL";
        //*pbNullTable = true;
        //sqlite3_close_v2(db);
        //errorInfo.ErrCode = 16080;
    }
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"ReadStatisticsOp", "");
    return  errorInfo;
}

/* DeleteStatisticsOp		        删除统计数据数据库
* @param[in]     int Fid 		        = 读取的工艺号(0标识全局统计数据   1-255表示工艺号)
* @param[in]     char* version		    = 统计数据版本号
* @return        errorInfo.ErrCode非0   = 删除结果失败
* @return        errorInfo.ErrCode = 0  = 删除结果成功
*/
ErrorInfo DeleteStatisticsOp(int Fid, char* version){
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    //sqlite3* db;
    sqlite3_stmt* stmt;
    char* err_msg = NULL;
    char delete_sql[] = "DELETE FROM StatisticsData WHERE Fid=? AND Ver=?";
    rc = sqlite3_prepare_v2(db, delete_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.ErrCode = 16082;
        goto Errorhandling;
    }
    rc = sqlite3_bind_int(stmt, 1, Fid);
    rc += sqlite3_bind_text(stmt, 2, version, -1, SQLITE_STATIC);
    if (rc == SQLITE_OK) {
        rc = sqlite3_step(stmt);
    }
    else {
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.ErrCode = 16083;
        goto Errorhandling;
    }
    if (rc != SQLITE_DONE) {
        printf("删除记录失败: %s\n", sqlite3_errmsg(db));
        sqlite3_finalize(stmt);
        //sqlite3_close_v2(db);
        errorInfo.ErrCode = 16084;
        goto Errorhandling;
    }
    sqlite3_finalize(stmt);
    //sqlite3_close_v2(db);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"DeleteStatisticsOp", "");
    return  errorInfo;
}

// 状态枚举定义
typedef enum {
    BEGIN_TRANSACTION,
    PREPARE_STATEMENT,
    BIND_PARAMETERS,
    EXECUTE_STATEMENT,
    COMMIT_TRANSACTION,
    ROLLBACK_TRANSACTION,
    CLEANUP,
    FINISHED
} DBWriteState;

// 状态机上下文结构体
typedef struct {
    DBWriteState currentState;
    sqlite3_stmt* stmt;
    char* errMsg;
    int rc;
    ErrorInfo errorInfo;
    bool isFatalError;
} WriteStateMachine;



// --- 重构后的单一日志记录函数接口 ---
ErrorInfo WriteErrorToSqlite(ErrorInfo errorInfo, bool isFatal, std::string fatalInfo, bool* pbFinishWrite) {
    // 1. 获取锁 (确保后续所有操作的线程安全)
    std::lock_guard<std::mutex> lock(g_database_mutex); 

    ErrorInfo resultInfo;
    memset(&resultInfo, 0, sizeof(ErrorInfo));
    //resultInfo.eErrLever = ErrorInfo::Ok; // 初始假设成功
    *pbFinishWrite = false;              // 初始假设写入失败，成功时再设置为 true

    sqlite3_stmt* stmt = nullptr; // 本地语句句柄
    int rc = SQLITE_OK;
    char* zErrMsg = nullptr;
    bool transaction_active = false;
    bool operation_failed = false; // 标记操作过程中是否发生失败

    // 2. 检查数据库句柄
    if (!db) {
        resultInfo.ErrCode = 16212; // 数据库句柄为空的错误代码
        //resultInfo.eErrLever = ErrorInfo::Error;
        snprintf(resultInfo.sErrDescribe, sizeof(resultInfo.sErrDescribe), "Database handle is null.");
        ErrorInfoPack(&resultInfo, (char*)"LogErrorToDatabase", "DB handle null");
        // 无需 finalize，因为 stmt 尚未创建
        return resultInfo; // 提前退出
    }

    // 3. 确定 SQL 语句并准备
    const char* sql = nullptr;
    if (isFatal) {
        sql = "INSERT INTO AlarmFatalRecord (AlarmCode,Brief,Fatalerrorinformation) VALUES (?,?,?);";
    }
    else {
        sql = "INSERT INTO AlarmRecord (AlarmCode,Brief) VALUES (?,?);";
    }

    // 准备语句
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        resultInfo.ErrCode = 16125; // 通用准备失败代码 (可自定义)
        //resultInfo.eErrLever = ErrorInfo::Error;
        snprintf(resultInfo.sErrDescribe, sizeof(resultInfo.sErrDescribe), "Prepare statement failed (%s): %s",
            isFatal ? "Fatal" : "Normal", sqlite3_errmsg(db));
        ErrorInfoPack(&resultInfo, (char*)"LogError_Prepare", sqlite3_errmsg(db));
        // stmt 为空或无效，无需 finalize
        return resultInfo; // 无法继续
    }
    // 5. 绑定参数 (仅当事务成功开始时)
    if (rc == SQLITE_OK) {
        rc = sqlite3_bind_int(stmt, 1, errorInfo.ErrCode);
        rc += sqlite3_bind_text(stmt, 2, errorInfo.sErrDescribe, -1, SQLITE_TRANSIENT); // 使用 SQLITE_TRANSIENT 让 SQLite 复制数据
        if (isFatal) {
            rc += sqlite3_bind_text(stmt, 3, fatalInfo.c_str(), -1, SQLITE_TRANSIENT);
        }

        if (rc != SQLITE_OK) {
            operation_failed = true;
            resultInfo.ErrCode = 16126; // 通用绑定失败代码 (可自定义)
            //resultInfo.eErrLever = ErrorInfo::Error;
            // sqlite3_errmsg(db) 在绑定失败时可能不提供具体信息，但可以尝试获取
            snprintf(resultInfo.sErrDescribe, sizeof(resultInfo.sErrDescribe), "Parameter binding failed (%s): rc=%d, db_err=%s",
                isFatal ? "Fatal" : "Normal", rc, sqlite3_errmsg(db));
            ErrorInfoPack(&resultInfo, (char*)"LogError_Bind", sqlite3_errmsg(db));
            sqlite3_finalize(stmt);
            return resultInfo; // 无法继续
        }
    }

    // 6. 执行 Step (带重试逻辑, 仅当绑定成功时)
    if (rc == SQLITE_OK) {
        rc = sqlite3_step(stmt);
        if (rc == SQLITE_DONE)
        {
            sqlite3_finalize(stmt);
            ErrorInfoPack(&errorInfo, (char*)"WriteErrorToSqlite", "");
            *pbFinishWrite = true;
            return resultInfo;
        }
        else {
            resultInfo.ErrCode = 16213;
            ErrorInfoPack(&resultInfo, (char*)"WriteErrorToSqlite", sqlite3_errmsg(db));
            sqlite3_finalize(stmt);
            //return resultInfo;

        }
    }
    return resultInfo;
    // 锁在 lock_guard 作用域结束时自动释放
}

//ErrorInfo ImportToSqlite(std::string tablename) {
//    std::string CsvPath = "/root/AlarmDefine.csv";
//    ErrorInfo errorInfo;
//    memset(&errorInfo, 0, sizeof(ErrorInfo));
//    std::string INSERT_DATA_SQL = "INSERT INTO " + tablename + " (Ver, Level, AlarmCode, Advice, BriefCN, BriefEN) VALUES (?, ?, ?, ?, ?, ?)";
//    sqlite3* db;
//    char* err_msg = nullptr;
//
//    // 创建数据库连接
//    int rc = sqlite3_open_v2(DATABASE_NAME, &db, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, 0);
//    if (rc != SQLITE_OK) {
//        std::cerr << "Failed to open database: " << sqlite3_errmsg(db) << std::endl;
//        sqlite3_close(db);
//        return errorInfo;
//    }
//    // 准备插入数据的 SQL 语句
//    sqlite3_stmt* stmt;
//    rc = sqlite3_prepare_v2(db, INSERT_DATA_SQL.c_str(), -1, &stmt, nullptr);
//    if (rc != SQLITE_OK) {
//        std::cerr << "Failed to prepare statement: " << sqlite3_errmsg(db) << std::endl;
//        sqlite3_close(db);
//        return errorInfo;
//    }
//    // 读取 CSV 文件并插入数据
//    std::ifstream file(CsvPath, std::ios::in);
//    std::string line;
//    getline(file, line); // 跳过表头行
//    rc = sqlite3_exec(db, "PRAGMA wal_checkpoint(TRUNCATE)", nullptr, nullptr, 0);
//    while (getline(file, line)) {
//        std::vector<std::string> fields;
//        std::istringstream ss(line);
//        std::string field;
//        while (getline(ss, field, '\t')) {
//            std::cerr << field.c_str() << " ";
//            fields.push_back(field);
//        }
//
//        sqlite3_bind_text(stmt, 1, fields[0].c_str(), -1, SQLITE_TRANSIENT);
//        sqlite3_bind_text(stmt, 2, fields[1].c_str(), -1, SQLITE_TRANSIENT);
//        sqlite3_bind_text(stmt, 3, fields[2].c_str(), -1, SQLITE_TRANSIENT);
//        sqlite3_bind_text(stmt, 4, fields[3].c_str(), -1, SQLITE_TRANSIENT);
//        sqlite3_bind_text(stmt, 5, fields[4].c_str(), -1, SQLITE_TRANSIENT);
//        sqlite3_bind_text(stmt, 6, fields[5].c_str(), -1, SQLITE_TRANSIENT);
//
//        rc = sqlite3_step(stmt);
//        if (rc != SQLITE_DONE) {
//            std::cerr << "Failed to insert data: " << sqlite3_errmsg(db) << std::endl;
//            sqlite3_finalize(stmt);
//            sqlite3_close(db);
//            return errorInfo;
//        }
//
//        sqlite3_reset(stmt);
//    }
//
//    sqlite3_finalize(stmt);
//    sqlite3_close(db);
//
//    std::cerr << "Data imported successfully!" << std::endl;
//    return errorInfo;
//}

ErrorInfo SqliteClear() 
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    //struct statvfs stat;
    const char* path = "/userdata";
    //long long availableSpace;
    //int MEMORY_LIMIT_GB = 95; // 5 GB
    //int MEMORY_REMAUNDER_GB = 90; // 5 GB
    int DELETE_BATCH_SIZE = 1;
    // 获取文件系统的统计信息
    //std::string delete_query = "DELETE FROM Result WHERE Id IN (SELECT Id FROM Result ORDER BY Creationtime ASC LIMIT 1);";
    std::string incrementalVacuum = "PRAGMA incremental_vacuum(1000);";
    std::string queryminid = "SELECT MIN(Id) FROM Result;";
    std::string deleterecord = "DELETE FROM Result WHERE Id = ?";
    sqlite3_stmt* selete_stmt;
    sqlite3_stmt* delete_stmt;
    int count =0;
    long pageSize = 0, pageCount = 0, freelistCount = 0;
    int minId = 0;
    //int rc = sqlite3_open_v2(DATABASE_RESULT, &db_result, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_NOMUTEX, 0);//SQLITE_OPEN_SHAREDCACHE
    //if (rc != SQLITE_OK) {
    //    printf("无法打开数据库: %s\n", sqlite3_errmsg(db));
    //    errorInfo.ErrCode = 16091;
    //    goto Errorhandling;
    //}
    rc = sqlite3_prepare_v3(db_result, queryminid.c_str(), queryminid.length(), 0, &selete_stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(selete_stmt);
        std::cout << "SQL语法错误，获取最小ID失败 " << minId << std::endl;
        errorInfo.ErrCode = 16121;
        goto Errorhandling;
    }
    // 执行查询
    if (sqlite3_step(selete_stmt) == SQLITE_ROW) {
        minId = sqlite3_column_int(selete_stmt, 0);  // 获取最小的Id
        std::cout << "最小的Id是: " << minId << std::endl;
        count = minId;
        sqlite3_finalize(selete_stmt);
    }
    else {
        std::cout << "表中没有记录。" << std::endl;
        sqlite3_finalize(selete_stmt);
    }
    rc = sqlite3_prepare_v3(db_result, deleterecord.c_str(), deleterecord.length(), 0, &delete_stmt, NULL);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(delete_stmt);
        errorInfo.ErrCode = 16122;
        std::cout << "SQL语法错误，删除指定ID记录失败 " << minId << std::endl;
        goto Errorhandling;
    }
    //rc = sqlite3_bind_int(delete_stmt, 1, DELETE_BATCH_SIZE);
    //if (rc != SQLITE_OK) {
    //    sqlite3_finalize(delete_stmt);
    //    //sqlite3_close_v2(db);
    //    errorInfo.ErrCode = 16083;
    //    goto Errorhandling;
    //}
    sqlite3_exec(db_result, "PRAGMA synchronous=OFF;", NULL, NULL, NULL);
    while (true) {
        //if (statvfs(path, &stat) != 0) {
        //    std::cerr << "Failed to get disk free space" << std::endl;
        //}
        //// 计算可用空间（以字节为单位）
        //availableSpace = stat.f_bsize * stat.f_bavail;
        //// 转换为 GB
        //availableSpace = availableSpace / 1024 / 1024 / 1024;
        //std::cout << "Available space: " << availableSpace << " GB" << std::endl;
        // 开始事务
        rc = sqlite3_exec(db_result, "PRAGMA page_size;", [](void* data, int argc, char** argv, char** colName) {
            *(int*)data = std::stoi(argv[0]);
            return 0;
            }, &pageSize, NULL);
        // 查询总页面数
        rc = sqlite3_exec(db_result, "PRAGMA page_count;", [](void* data, int argc, char** argv, char** colName) {
            *(int*)data = std::stoi(argv[0]);
            return 0;
            }, &pageCount, NULL);
        rc = sqlite3_exec(db_result, "PRAGMA freelist_count;", [](void* data, int argc, char** argv, char** colName) {
            *(int*)data = std::stoi(argv[0]);
            return 0;
            }, &freelistCount, NULL);
        long totalSpace = (pageSize * pageCount / 1024 / 1024 / 1024 ) + 1;
        long usedSpace = (pageSize * (pageCount - freelistCount) / 1024 / 1024 / 1024) +1;
        usleep(5000000);
        //std::cout << "SQLite 文件总大小: " << totalSpace << "G" << std::endl;
        //std::cout << "有效数据占用空间: " << usedSpace << "G" << std::endl;
        if (usedSpace > MEMORY_LIMIT_GB) {
            while (usedSpace > MEMORY_REMAUNDER_GB) {
                sqlite3_exec(db_result, "BEGIN TRANSACTION;", NULL, NULL, NULL);
                // 构建删除语句，每次删除100条记录
                //snprintf(sql_delete, sizeof(sql_delete), "DELETE FROM Result WHERE Id IN (SELECT Id FROM Result WHERE Id <= %d ORDER BY Id LIMIT 10);", maxIdToDelete);
                for (int i = 0; i < 20; ++i) {
                    rc_result = sqlite3_bind_int(delete_stmt, 1, count);
                    if (rc_result == SQLITE_OK) {
                        rc_result = sqlite3_step(delete_stmt); 
                        if (rc_result != SQLITE_DONE) {
                            printf("执行删除记录失败: %s\n", sqlite3_errmsg(db_result));
                            //sqlite3_exec(db_result, "ROLLBACK;", NULL, NULL, NULL);
                            errorInfo.ErrCode = 16124;
                            sqlite3_finalize(delete_stmt);

                            break; // 如果失败，退出循环
                        }
                    }
                    else {
                        printf("绑定删除记录ID字段失败: %s\n", sqlite3_errmsg(db_result));
                        //sqlite3_exec(db_result, "ROLLBACK;", NULL, NULL, NULL);
                        errorInfo.ErrCode = 16123;
                        sqlite3_finalize(delete_stmt);
                    }
                   
                    count++;
                    sqlite3_reset(delete_stmt);
                    //snprintf(sql_delete, sizeof(sql_delete), "DELETE FROM Result WHERE Id = %d", count);
                    //rc_result = sqlite3_exec(db_result, sql_delete, nullptr, nullptr, nullptr);
                    //if (rc_result != SQLITE_OK) {
                    //    printf("删除记录失败: %s\n", sqlite3_errmsg(db_result));
                    //    sqlite3_exec(db_result, "ROLLBACK;", NULL, NULL, NULL);
                    //    break; // 如果失败，退出循环
                    //}
                }

                // 执行删除操作
                //rc_result = sqlite3_exec(db_result, sql_delete, NULL, NULL, NULL);
                

                // 提交事务
                sqlite3_exec(db_result, "COMMIT;", NULL, NULL, NULL);
                //std::cout << "删除成功"   << std::endl;
                rc = sqlite3_exec(db_result, "PRAGMA page_size;", [](void* data, int argc, char** argv, char** colName) {
                    *(int*)data = std::stoi(argv[0]);
                    return 0;
                    }, &pageSize, NULL);
                // 查询总页面数
                rc = sqlite3_exec(db_result, "PRAGMA page_count;", [](void* data, int argc, char** argv, char** colName) {
                    *(int*)data = std::stoi(argv[0]);
                    return 0;
                    }, &pageCount, NULL);
                rc = sqlite3_exec(db_result, "PRAGMA freelist_count;", [](void* data, int argc, char** argv, char** colName) {
                    *(int*)data = std::stoi(argv[0]);
                    return 0;
                    }, &freelistCount, NULL);
                totalSpace = (pageSize * pageCount / 1024 / 1024 / 1024) + 1;
                usedSpace = (pageSize * (pageCount - freelistCount) / 1024 / 1024 / 1024) + 1;
                //std::cout << "SQLite 文件总大小: " << totalSpace << " 字节" << std::endl;
                //std::cout << "有效数据占用空间: " << usedSpace << " 字节" << std::endl;
                //// 检查是否还有记录需要删除
                //if (statvfs(path, &stat) != 0) {
                //    std::cerr << "Failed to get disk free space" << std::endl;
                //}
                //// 计算可用空间（以字节为单位）
                //availableSpace = stat.f_bsize * stat.f_bavail;
                //// 转换为 GB
                //availableSpace = availableSpace / 1024 / 1024 / 1024;
                //std::cout << "Available space: " << availableSpace << " GB" << std::endl;
                /*snprintf(sql_delete, sizeof(sql_delete), "SELECT EXISTS(SELECT 1 FROM Result WHERE Id <= %d);", maxIdToDelete);

                sqlite3_prepare_v2(db_result, sql_delete, -1, &stmt_check, NULL);
                sqlite3_step(stmt_check);
                exists = sqlite3_column_int(stmt_check, 0);
                sqlite3_finalize(stmt_check);*/
                usleep(1000000);
                //std::cerr << "删除成功" << std::endl;
                //if (!exists) {
                //    break; // 如果没有更多记录，则退出循环
                //}
            }
        }
    }           
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"DeleteStatisticsOp", "");
    return  errorInfo;
}

/* WriteSdoFromDataBase		                    写入SdoData函数
* @param[in]     uint16* pWriteCount		    = 写入记录的数量
* @param[in]     SdoDataUnit* pSdoData		    = 写入记录的内容
*/
// 批量写入数据库
ErrorInfo WriteSdoFromDataBase(uint16* pWriteCount, SdoDataUnit* pSdoData) {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    const char* sql = "INSERT OR REPLACE INTO SdoData (skey, ukey, name, var) VALUES (?, ?, ?, ?);";
    sqlite3_stmt* stmt = nullptr;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16202;
        goto Errorhandling;
    }
    // 开启事务以提高批量写入效率
    sqlite3_exec(db, "BEGIN TRANSACTION;", nullptr, nullptr, nullptr);
    for (int i = 0; i < *pWriteCount; i++) {
        // 绑定参数：依次写入 sKey, iKey, sName, sVar
        sqlite3_bind_text(stmt, 1, pSdoData[i].sKey, -1, SQLITE_STATIC);
        sqlite3_bind_int(stmt, 2, pSdoData[i].iKey);
        sqlite3_bind_text(stmt, 3, pSdoData[i].sName, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 4, pSdoData[i].sVar, -1, SQLITE_STATIC);

        rc = sqlite3_step(stmt);
        if (rc != SQLITE_DONE) {
            sqlite3_finalize(stmt);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16203;
            sqlite3_exec(db, "ROLLBACK;", nullptr, nullptr, nullptr);
            goto Errorhandling;
        }
        // 重置语句，准备下一次绑定
        sqlite3_reset(stmt);
        sqlite3_clear_bindings(stmt);
    }
    sqlite3_exec(db, "COMMIT;", nullptr, nullptr, nullptr);
    sqlite3_finalize(stmt);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"WriteSdoFromDataBase", "");
    return errorInfo;
}


/* ReadSdoFromDataBase		                    读取SdoData函数
* @param[in]     uint16* pReadCount 		    = 读取SdoData记录的数量
* @param[in]     SdoDataUnit* pSdoData		    = 读取SdoData记录的内容
*/
// 批量读取数据库：根据 pSdoData 数组中已有的 sKey, iKey, sName 查找对应记录，将数据库中 var 字段的值赋给 sVar
ErrorInfo ReadSdoFromDataBase(uint16* pReadCount, SdoDataUnit* pSdoData) {
    ErrorInfo errorInfo;
    ErrorInfoPack(&errorInfo, (char*)"ReadSdoFromDataBase", "");

    const char* sql = "SELECT var FROM SdoData WHERE skey = ? AND ukey = ? AND name = ? LIMIT 1;";
    sqlite3_stmt* stmt = nullptr;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr);
    if (rc != SQLITE_OK) {
        sqlite3_finalize(stmt);
        errorInfo.eErrLever = Error;
        errorInfo.ErrCode = 16204;
        goto Errorhandling;
    }
    for (int i = 0; i < *pReadCount; i++) {
        // 绑定查询条件：sKey, iKey, sName
        sqlite3_bind_text(stmt, 1, pSdoData[i].sKey, -1, SQLITE_STATIC);
        sqlite3_bind_int(stmt, 2, pSdoData[i].iKey);
        sqlite3_bind_text(stmt, 3, pSdoData[i].sName, -1, SQLITE_STATIC);

        rc = sqlite3_step(stmt);
        if (rc == SQLITE_ROW) {
            // 取出 var 字段内容
            const unsigned char* varText = sqlite3_column_text(stmt, 0);
            if (varText) {
                std::strncpy(pSdoData[i].sVar, reinterpret_cast<const char*>(varText), sizeof(pSdoData[i].sVar) - 1);
                pSdoData[i].sVar[sizeof(pSdoData[i].sVar) - 1] = '\0';
            }
        }
        else if (rc == SQLITE_DONE) {
            // 如果未查到记录，则将 sVar 置为空字符串
            pSdoData[i].sVar[0] = '\0';
        }
        else {
            sqlite3_finalize(stmt);
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 16205;
            goto Errorhandling;
        }
        // 重置语句以供下一次查询
        sqlite3_reset(stmt);
        sqlite3_clear_bindings(stmt);
    }
    sqlite3_finalize(stmt);
    Errorhandling:
    ErrorInfoPack(&errorInfo, (char*)"ReadSdoFromDataBase", "");
    return errorInfo;
}