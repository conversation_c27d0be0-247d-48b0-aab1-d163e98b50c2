{"C_Cpp_Runner.cCompilerPath": "gcc", "C_Cpp_Runner.cppCompilerPath": "g++", "C_Cpp_Runner.debuggerPath": "gdb", "C_Cpp_Runner.cStandard": "", "C_Cpp_Runner.cppStandard": "", "C_Cpp_Runner.msvcBatchPath": "C:/Program Files/Microsoft Visual Studio/VR_NR/Community/VC/Auxiliary/Build/vcvarsall.bat", "C_Cpp_Runner.useMsvc": false, "C_Cpp_Runner.warnings": ["-Wall", "-Wextra", "-Wpedantic", "-W<PERSON>dow", "-Wformat=2", "-Wcast-align", "-Wconversion", "-Wsign-conversion", "-W<PERSON><PERSON>-dereference"], "C_Cpp_Runner.msvcWarnings": ["/W4", "/permissive-", "/w14242", "/w14287", "/w14296", "/w14311", "/w14826", "/w44062", "/w44242", "/w14905", "/w14906", "/w14263", "/w44265", "/w14928"], "C_Cpp_Runner.enableWarnings": true, "C_Cpp_Runner.warningsAsError": false, "C_Cpp_Runner.compilerArgs": [], "C_Cpp_Runner.linkerArgs": [], "C_Cpp_Runner.includePaths": [], "C_Cpp_Runner.includeSearch": ["*", "**/*"], "C_Cpp_Runner.excludeSearch": ["**/build", "**/build/**", "**/.*", "**/.*/**", "**/.vscode", "**/.vscode/**"], "C_Cpp_Runner.useAddressSanitizer": false, "C_Cpp_Runner.useUndefinedSanitizer": false, "C_Cpp_Runner.useLeakSanitizer": false, "C_Cpp_Runner.showCompilationTime": false, "C_Cpp_Runner.useLinkTimeOptimization": false, "C_Cpp_Runner.msvcSecureNoWarnings": false, "files.associations": {"邓comm": "c", "array": "c", "atomic": "c", "bit": "c", "cctype": "c", "chrono": "c", "clocale": "c", "cmath": "c", "compare": "c", "concepts": "c", "condition_variable": "c", "cstdarg": "c", "cstddef": "c", "cstdint": "c", "cstdio": "c", "cstdlib": "c", "cstring": "c", "ctime": "c", "cwchar": "c", "cwctype": "c", "deque": "c", "map": "c", "string": "c", "unordered_map": "c", "vector": "c", "exception": "c", "algorithm": "c", "functional": "c", "iterator": "c", "memory": "c", "memory_resource": "c", "numeric": "c", "random": "c", "ratio": "c", "string_view": "c", "system_error": "c", "tuple": "c", "type_traits": "c", "utility": "c", "fstream": "c", "initializer_list": "c", "iomanip": "c", "iosfwd": "c", "iostream": "c", "istream": "c", "limits": "c", "mutex": "c", "new": "c", "numbers": "c", "ostream": "c", "semaphore": "c", "sstream": "c", "stdexcept": "c", "stop_token": "c", "streambuf": "c", "thread": "c", "cinttypes": "c", "typeinfo": "c"}}